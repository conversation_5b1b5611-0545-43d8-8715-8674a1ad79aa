{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug @apps/api-gateway with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "@apps/api-gateway"], "env": {"NODE_OPTIONS": "--inspect=9229"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/api-gateway/dist/**/*.(m|c|)js", "!**/node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug @easy-flow/scrum-hub with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "@easy-flow/scrum-hub"], "env": {"NODE_OPTIONS": "--inspect=9230"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/scrum-hub/dist/**/*.(m|c|)js", "!**/node_modules/**"]}]}