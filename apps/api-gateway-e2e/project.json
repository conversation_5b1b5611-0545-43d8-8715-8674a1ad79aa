{"name": "@apps/api-gateway-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["@apps/api-gateway"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/api-gateway-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["@apps/scrum-hub:serve-staging", "@apps/api-gateway:serve-staging"]}}}