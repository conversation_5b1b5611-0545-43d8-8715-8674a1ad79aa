import { HttpStatus } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { ApiResponsePayload } from '@libs/common/api';
import {
  FlowModel,
  FlowProcessModel,
  CreateFlowDto,
  CreateFlowProcessDto,
  UpdateFlowProcessDto,
  PartialUpdateFlowProcessDto,
  MoveFlowProcessDto,
} from '@libs/models';
import { expectFailedApiResponse, expectSuccessfulApiResponse } from '@libs/tests/api';

describe('Flow Processes Module (e2e)', () => {
  let rootFlowId: string;
  beforeAll(async () => {
    // create flow
    const createDto: CreateFlowDto = {
      name: 'Flow',
      description: 'Flow description',
      prevId: null,
    };
    const res = await axios.request<ApiResponsePayload<FlowModel>>({
      method: 'post',
      url: '/api/flows',
      data: createDto,
    });
    const { data: flow } = res.data;
    rootFlowId = flow.id;
  });

  const createFlowProcess = async (process: CreateFlowProcessDto): Promise<FlowProcessModel> => {
    const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
      method: 'post',
      url: `/api/flows/${rootFlowId}/processes`,
      data: process,
    });
    return res.data.data;
  };

  const deleteFlowProcess = async (processId: string): Promise<AxiosResponse<ApiResponsePayload<null>>> => {
    const res = await axios.request<ApiResponsePayload<null>>({
      method: 'delete',
      url: `/api/flows/${rootFlowId}/processes/${processId}`,
    });
    return res;
  };

  const getFlowProcess = async (processId: string): Promise<FlowProcessModel> => {
    const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
      method: 'get',
      url: `/api/flows/${rootFlowId}/processes/${processId}`,
    });
    return res.data.data;
  };

  const expectFlowProcess = (flowProcess: FlowProcessModel, expected: Partial<FlowProcessModel>) => {
    expect(flowProcess).toHaveProperty('id');
    if (expected.id) expect(flowProcess.id).toEqual(expected.id);

    expect(flowProcess).toHaveProperty('name', expected.name);
    expect(flowProcess).toHaveProperty('description', expected.description);
    expect(flowProcess).toHaveProperty('parentId', expected.parentId);
    expect(flowProcess).toHaveProperty('prevId', expected.prevId);

    expect(flowProcess).toHaveProperty('mPath');
    if (expected.mPath) expect(flowProcess.mPath).toEqual(expected.mPath);

    // assert that there are only 5 properties
    expect(Object.keys(flowProcess)).toHaveLength(6);
  };

  const createFlowProcessRootDto: CreateFlowProcessDto = {
    name: 'Flow process',
    description: 'Flow process description',
    parentId: null,
    prevId: null,
  };

  describe('/api/flows/:flowId/processes POST (create flow)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/abc/processes`,
            data: createFlowProcessRootDto,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });

      describe('name', () => {
        it('throw BadRequestException if name is not provided', async () => {
          const data = {
            description: 'Flow description',
            parentId: null,
            prevId: null,
          } as CreateFlowProcessDto;
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if name is provided', async () => {
          const createDto: CreateFlowProcessDto = {
            name: 'Flow process',
            description: 'Flow process description',
            parentId: null,
            prevId: null,
          };
          const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data: createDto,
          });
          expectSuccessfulApiResponse(res, HttpStatus.CREATED);
        });
      });

      describe('description', () => {
        it('throw BadRequestException if description is not provided', async () => {
          const data: CreateFlowProcessDto = {
            name: 'Flow process',
            parentId: null,
            prevId: null,
          } as CreateFlowProcessDto;
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if description is null', async () => {
          const createDto: CreateFlowProcessDto = {
            name: 'Flow process',
            description: null,
            parentId: null,
            prevId: null,
          };
          const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data: createDto,
          });
          expectSuccessfulApiResponse(res, HttpStatus.CREATED);
        });

        it('should pass if description is empty string and the description should be null in the result', async () => {
          const createDto: CreateFlowProcessDto = {
            name: 'Flow process',
            description: '',
            parentId: null,
            prevId: null,
          };
          const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data: createDto,
          });
          expectSuccessfulApiResponse(res, HttpStatus.CREATED);
          expect(res.data.data).toHaveProperty('description', null);
        });
      });

      describe('parentId', () => {
        it('throw BadRequestException if parentId is not provided', async () => {
          const data: CreateFlowProcessDto = {
            name: 'Flow process',
            description: 'Flow process description',
            prevId: null,
          } as CreateFlowProcessDto;
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('throw BadRequestException if parentId is not a numerical string', async () => {
          const data: CreateFlowProcessDto = {
            name: 'Flow process',
            description: 'Flow process description',
            parentId: 'abc',
            prevId: null,
          } as CreateFlowProcessDto;
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if parentId is null', async () => {
          const createDto: CreateFlowProcessDto = {
            name: 'Flow process',
            description: 'Flow process description',
            parentId: null,
            prevId: null,
          };
          const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data: createDto,
          });
          expectSuccessfulApiResponse(res, HttpStatus.CREATED);
        });
      });

      describe('prevId', () => {
        it('throw BadRequestException if prevId is not provided', async () => {
          const data: CreateFlowProcessDto = {
            name: 'Flow process',
            description: 'Flow process description',
            parentId: null,
          } as CreateFlowProcessDto;
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('throw BadRequestException if prevId is not a numerical string', async () => {
          const createDto: CreateFlowProcessDto = {
            name: 'Flow process',
            description: 'Flow process description',
            parentId: null,
            prevId: 'abc',
          };
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data: createDto,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if prevId is null', async () => {
          const createDto: CreateFlowProcessDto = {
            name: 'Flow process',
            description: 'Flow process description',
            parentId: null,
            prevId: null,
          };
          const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes`,
            data: createDto,
          });
          expectSuccessfulApiResponse(res, HttpStatus.CREATED);
        });
      });
    });

    describe('flow process creating', () => {
      it('throw ConflictException if flow does not exist', async () => {
        const createDto: CreateFlowProcessDto = {
          name: 'Flow process',
          description: 'Flow process description',
          parentId: '1',
          prevId: null,
        };
        const res = await axios.request({
          method: 'post',
          url: '/api/flows/12345/processes',
          data: createDto,
        });
        expectFailedApiResponse(res, HttpStatus.CONFLICT, 'PARENT_PROCESS_NOT_FOUND');
      });

      it('throw ConflictException if parent process does not exist', async () => {
        const createDto: CreateFlowProcessDto = {
          name: 'Flow process',
          description: 'Flow process description',
          parentId: '12345',
          prevId: null,
        };
        const res = await axios.request({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes`,
          data: createDto,
        });
        expectFailedApiResponse(res, HttpStatus.CONFLICT, 'PARENT_PROCESS_NOT_FOUND');
      });

      it('creates new flow process and returns the created flow process', async () => {
        const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes`,
          data: createFlowProcessRootDto,
        });
        expectSuccessfulApiResponse(res, HttpStatus.CREATED);
        expectFlowProcess(res.data.data, createFlowProcessRootDto);
      });

      it('creates new flow process with parent (+ validate mPath) process and returns the created flow process', async () => {
        const parentProcess = await createFlowProcess(createFlowProcessRootDto);

        const createDto: CreateFlowProcessDto = {
          name: 'Flow process',
          description: 'Flow process description',
          parentId: parentProcess.id, // parent process id
          prevId: null,
        };
        const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes`,
          data: createDto,
        });
        expectSuccessfulApiResponse(res, HttpStatus.CREATED);

        // validate created process
        const { data: createdProcess } = res.data;
        expectFlowProcess(createdProcess, createDto);

        // validate process mPath
        expect(createdProcess.mPath.startsWith(parentProcess.mPath)).toBeTruthy();
      });

      it('creates new flow process with children and check child prevId', async () => {
        const parentProcess = await createFlowProcess(createFlowProcessRootDto);

        const createChildDto: CreateFlowProcessDto = {
          name: 'Flow process',
          description: 'Flow process description',
          parentId: parentProcess.id, // parent process id
          prevId: null,
        };
        let childProcess1 = await createFlowProcess(createChildDto);
        let childProcess2 = await createFlowProcess(createChildDto);

        // childProcess2 -> childProcess1

        childProcess2 = await getFlowProcess(childProcess2.id);
        expect(childProcess2).toHaveProperty('prevId', null);

        childProcess1 = await getFlowProcess(childProcess1.id);
        expect(childProcess1).toHaveProperty('prevId', childProcess2.id);
      });

      it('creates new flow process and moves the flow process with the same prevId (null) below', async () => {
        // create first flow process with prevId = null
        let process1 = await createFlowProcess(createFlowProcessRootDto);
        expect(process1).toHaveProperty('prevId', null);

        // create second flow process with prevId = null
        const process2 = await createFlowProcess(createFlowProcessRootDto);
        expect(process2).toHaveProperty('prevId', null);

        // get first flow process
        process1 = await getFlowProcess(process1.id);
        expect(process1).toHaveProperty('prevId', process2.id);
      });

      it('creates new flow process and moves the flow process with the same prevId (id) below', async () => {
        // create first flow process with prevId = null
        let process1 = await createFlowProcess(createFlowProcessRootDto);
        expect(process1).toHaveProperty('prevId', null);

        // create second flow process with prevId = null
        // order process2 -> process1
        const process2 = await createFlowProcess(createFlowProcessRootDto);
        expect(process2).toHaveProperty('prevId', null);

        // create third flow process with prevId = process2.id
        // order process2 -> process3 -> process1
        const process3 = await createFlowProcess({ ...createFlowProcessRootDto, prevId: process2.id });
        expect(process3).toHaveProperty('prevId', process2.id);

        // check first flow process
        process1 = await getFlowProcess(process1.id);
        expect(process1).toHaveProperty('prevId', process3.id);
      });

      it('should handle concurrent requests safely when creating flow process (race condition test)', async () => {
        const responses = await Promise.all(
          Array(3)
            .fill(0)
            .map(() => createFlowProcess(createFlowProcessRootDto)),
        );

        expect(responses.every(f => f.prevId === null)).toBeTruthy();

        const processes = await Promise.all(responses.map(f => getFlowProcess(f.id)));

        // check if there is only one process with prevId = null
        const root = processes.find(f => f.prevId === null);
        expect(root).toBeDefined();

        if (!root) throw new Error('Root not found');

        // check if all processes are in a chain
        const chain = [root];
        let current = processes.find(f => f.prevId === root.id);

        while (current) {
          chain.push(current);
          current = processes.find(f => f.prevId === current?.id);
        }

        // check if all processes are in the chain
        expect(chain.length).toBe(processes.length);
      });
    });
  });

  describe('/api/flows/:flowId/processes/:processId DELETE (delete flow process)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const res = await axios.request({ method: 'delete', url: '/api/flows/abc/processes/1' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });

      describe('processId', () => {
        it('throws BadRequestException if processId is not a numerical string', async () => {
          const res = await axios.request({ method: 'delete', url: '/api/flows/1/processes/abc' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });
    });

    describe('flow process deletion', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const res = await axios.request({ method: 'delete', url: '/api/flows/12345/processes/1' });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('throws NotFoundException if process does not exist', async () => {
        const res = await axios.request({
          method: 'delete',
          url: `/api/flows/${rootFlowId}/processes/12345`,
        });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('deletes flow process and returns null', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);

        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'delete',
          url: `/api/flows/${rootFlowId}/processes/${process.id}`,
        });
        expectSuccessfulApiResponse(res);
        expect(res.data.data).toBeNull();
      });

      it('deletes flow process with children', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);

        const childProcess1 = await createFlowProcess({ ...createFlowProcessRootDto, parentId: process.id });
        const childProcess2 = await createFlowProcess({ ...createFlowProcessRootDto, parentId: process.id });

        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'delete',
          url: `/api/flows/${rootFlowId}/processes/${process.id}`,
        });

        expectSuccessfulApiResponse(res);

        const childProcess1Res = await axios.request({
          method: 'get',
          url: `/api/flows/${rootFlowId}/processes/${childProcess1.id}`,
        });
        expectFailedApiResponse(childProcess1Res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');

        const childProcess2Res = await axios.request({
          method: 'get',
          url: `/api/flows/${rootFlowId}/processes/${childProcess2.id}`,
        });
        expectFailedApiResponse(childProcess2Res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('deletes flow process and moves the next flow process above', async () => {
        // create first flow process with prevId = null
        const process1 = await createFlowProcess(createFlowProcessRootDto);

        // create second flow process with prevId = process1.id
        let process2 = await createFlowProcess({ ...createFlowProcessRootDto, prevId: process1.id });

        // delete first flow process
        await axios.request({ method: 'delete', url: `/api/flows/${rootFlowId}/processes/${process1.id}` });

        // get second flow process
        process2 = await getFlowProcess(process2.id);
        expect(process2).toHaveProperty('prevId', null);
      });

      it('should handle concurrent requests safely when deleting flow process (race condition test)', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);

        const responses = await Promise.all(
          Array(3)
            .fill(0)
            .map(() => deleteFlowProcess(process.id)),
        );

        expect(responses[0].status).toBe(HttpStatus.OK);
        expect(responses[1].status).toBe(HttpStatus.NOT_FOUND);
        expect(responses[2].status).toBe(HttpStatus.NOT_FOUND);
      });
    });
  });

  describe('/api/flows/:flowId/processes/:processId GET (get flow process)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const res = await axios.request({ method: 'get', url: '/api/flows/abc/processes/1' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });
      describe('processId', () => {
        it('throws BadRequestException if processId is not a numerical string', async () => {
          const res = await axios.request({ method: 'get', url: '/api/flows/1/processes/abc' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });
    });

    describe('flow process retrieval', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const res = await axios.request({ method: 'get', url: '/api/flows/12345/processes/1' });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('throws NotFoundException if process does not exist', async () => {
        const res = await axios.request({
          method: 'get',
          url: `/api/flows/${rootFlowId}/processes/12345`,
        });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('returns flow process', async () => {
        // create flow process
        const process = await createFlowProcess(createFlowProcessRootDto);

        const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
          method: 'get',
          url: `/api/flows/${rootFlowId}/processes/${process.id}`,
        });
        expectSuccessfulApiResponse(res);
        expectFlowProcess(res.data.data, process);
      });
    });
  });

  describe('/api/flows/:flowId/processes GET (get all flow processes)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const res = await axios.request({ method: 'get', url: '/api/flows/abc/processes' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });
    });

    describe('flow process retrieval', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const res = await axios.request({ method: 'get', url: '/api/flows/12345/processes' });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'FLOW_NOT_FOUND');
      });

      it('returns all flow processes', async () => {
        // create flow process
        const process = await createFlowProcess(createFlowProcessRootDto);

        const res = await axios.request<ApiResponsePayload<FlowProcessModel[]>>({
          method: 'get',
          url: `/api/flows/${rootFlowId}/processes`,
        });
        expectSuccessfulApiResponse(res);
        expect(res.data.data).toBeInstanceOf(Array);
        // find the created flow process
        expect(res.data.data.find(f => f.id === process.id)).toBeDefined();
      });
    });
  });

  describe('/api/flows/:flowId/processes/:processId PUT (update flow process)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const res = await axios.request({ method: 'put', url: '/api/flows/abc/processes/1' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });

      describe('processId', () => {
        it('throws BadRequestException if processId is not a numerical string', async () => {
          const res = await axios.request({ method: 'put', url: '/api/flows/1/processes/abc' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });

      describe('name', () => {
        it('throw BadRequestException if name is not provided', async () => {
          const data = {
            description: 'Updated description',
          } as UpdateFlowProcessDto;
          const res = await axios.request({
            method: 'put',
            url: `/api/flows/${rootFlowId}/processes/1`,
            data,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if name is empty string', async () => {
          const updateDto: UpdateFlowProcessDto = {
            name: '',
            description: 'Updated description',
          };
          const res = await axios.request({
            method: 'put',
            url: `/api/flows/${rootFlowId}/processes/1`,
            data: updateDto,
          });
          expectSuccessfulApiResponse(res);
        });
      });

      describe('description', () => {
        it('throw BadRequestException if description is not provided', async () => {
          const data = {
            name: 'Updated flow process',
          } as UpdateFlowProcessDto;
          const res = await axios.request({
            method: 'put',
            url: `/api/flows/${rootFlowId}/processes/1`,
            data,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if description is null', async () => {
          const updateDto: UpdateFlowProcessDto = {
            name: 'Updated flow process',
            description: null,
          };
          const res = await axios.request({
            method: 'put',
            url: `/api/flows/${rootFlowId}/processes/1`,
            data: updateDto,
          });
          expectSuccessfulApiResponse(res);
        });

        it('should pass if description is empty string and the description should be null in the result', async () => {
          const updateDto: UpdateFlowProcessDto = {
            name: 'Updated flow process',
            description: '',
          };
          const res = await axios.request({
            method: 'put',
            url: `/api/flows/${rootFlowId}/processes/1`,
            data: updateDto,
          });
          expectSuccessfulApiResponse(res);
          const { data } = res.data;
          expect(data).toHaveProperty('description', null);
        });
      });

      describe('parentId', () => {
        it('should not update parentId if parentId is provided', async () => {
          const process1 = await createFlowProcess(createFlowProcessRootDto);
          let childProcess2 = await createFlowProcess({ ...createFlowProcessRootDto, parentId: process1.id });

          const updatedData = {
            name: 'Updated flow process',
            description: 'Updated description',
            parentId: '1',
          } as UpdateFlowProcessDto;

          const res = await axios.request({
            method: 'put',
            url: `/api/flows/${rootFlowId}/processes/${childProcess2.id}`,
            data: updatedData,
          });
          expectSuccessfulApiResponse(res);

          // parentId must be the same
          childProcess2 = await getFlowProcess(childProcess2.id);
          expect(childProcess2).toHaveProperty('parentId', process1.id);
        });
      });

      describe('prevId', () => {
        it('should not update prevId if prevId is provided', async () => {
          const process = await createFlowProcess(createFlowProcessRootDto);

          const data = {
            name: 'Updated flow process',
            description: 'Updated description',
            prevId: '1',
          } as UpdateFlowProcessDto;

          const res = await axios.request({
            method: 'put',
            url: `/api/flows/${rootFlowId}/processes/${process.id}`,
            data,
          });

          expectSuccessfulApiResponse(res);

          const updatedProcess = await getFlowProcess(process.id);
          expect(updatedProcess).toHaveProperty('prevId', null);
        });
      });
    });

    describe('flow process update', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const updateDto: UpdateFlowProcessDto = {
          name: 'Updated flow process',
          description: 'Updated description',
        };
        const res = await axios.request({
          method: 'put',
          url: '/api/flows/12345/processes/1',
          data: updateDto,
        });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('throws NotFoundException if process does not exist', async () => {
        const updateDto: UpdateFlowProcessDto = {
          name: 'Updated flow process',
          description: 'Updated description',
        };
        const res = await axios.request({
          method: 'put',
          url: `/api/flows/${rootFlowId}/processes/12345`,
          data: updateDto,
        });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('updates flow process and returns the updated process', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);

        const updatedDto: UpdateFlowProcessDto = {
          name: 'Updated flow process',
          description: 'Updated description',
        };
        const res = await axios.request<ApiResponsePayload<FlowProcessModel>>({
          method: 'put',
          url: `/api/flows/${rootFlowId}/processes/${process.id}`,
          data: updatedDto,
        });
        expectSuccessfulApiResponse(res);

        const { data } = res.data;
        expectFlowProcess(data, { ...process, ...updatedDto });
      });

      it('should handle concurrent requests safely when updating flow process (race condition test)', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);
        const responses = await Promise.all(
          Array(3)
            .fill(0)
            .map((_v, index) => {
              const updatedDto: PartialUpdateFlowProcessDto = {
                description: `Updated flow process ${index}`,
              };
              return axios.request({
                method: 'patch',
                url: `/api/flows/${rootFlowId}/processes/${process.id}`,
                data: updatedDto,
              });
            }),
        );

        expect(responses.every(res => (res.status as HttpStatus) === HttpStatus.OK)).toBeTruthy();

        const updatedProcess = await getFlowProcess(process.id);
        expect(['Updated flow process 1', 'Updated flow process 2']).toContain(updatedProcess.description);
      });
    });
  });

  describe('/api/flows/:flowId/processes/:processId PATCH (partial update flow process)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const res = await axios.request({ method: 'patch', url: '/api/flows/abc/processes/1' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });

      describe('processId', () => {
        it('throws BadRequestException if processId is not a numerical string', async () => {
          const res = await axios.request({ method: 'patch', url: '/api/flows/1/processes/abc' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });

      describe('name', () => {
        it('should pass if name is not provided', async () => {
          const updateDto: PartialUpdateFlowProcessDto = {
            description: 'Updated description',
          };
          const res = await axios.request({
            method: 'patch',
            url: `/api/flows/${rootFlowId}/processes/1`,
            data: updateDto,
          });
          expectSuccessfulApiResponse(res);
        });
      });

      describe('description', () => {
        it('should pass if description is not provided', async () => {
          const updateDto: PartialUpdateFlowProcessDto = {
            name: 'Updated flow process',
          };
          const res = await axios.request({
            method: 'patch',
            url: `/api/flows/${rootFlowId}/processes/1`,
            data: updateDto,
          });
          expectSuccessfulApiResponse(res);
        });

        it('should pass if description is null', async () => {
          const updateDto: PartialUpdateFlowProcessDto = {
            description: null,
          };
          const res = await axios.request({
            method: 'patch',
            url: `/api/flows/${rootFlowId}/processes/1`,
            data: updateDto,
          });
          expectSuccessfulApiResponse(res);
        });

        it('should pass if description is empty string and the description should be null in the result', async () => {
          const process = await createFlowProcess(createFlowProcessRootDto);

          const updatedDto: PartialUpdateFlowProcessDto = {
            description: '',
          };
          const res = await axios.request({
            method: 'patch',
            url: `/api/flows/${rootFlowId}/processes/${process.id}`,
            data: updatedDto,
          });
          expectSuccessfulApiResponse(res);

          const updatedProcess = await getFlowProcess(process.id);
          expect(updatedProcess).toHaveProperty('description', null);
        });
      });

      describe('parentId', () => {
        it('should not update parentId if parentId is provided', async () => {
          const process = await createFlowProcess(createFlowProcessRootDto);

          const data = {
            parentId: '1',
          } as PartialUpdateFlowProcessDto;

          const res = await axios.request({
            method: 'patch',
            url: `/api/flows/${rootFlowId}/processes/${process.id}`,
            data,
          });
          expectSuccessfulApiResponse(res);

          const updatedProcess = await getFlowProcess(process.id);
          expect(updatedProcess).toHaveProperty('parentId', null);
        });
      });

      describe('prevId', () => {
        it('should not update prevId if prevId is provided', async () => {
          const process = await createFlowProcess(createFlowProcessRootDto);

          const data = {
            prevId: '1',
          } as PartialUpdateFlowProcessDto;

          const res = await axios.request({
            method: 'patch',
            url: `/api/flows/${rootFlowId}/processes/${process.id}`,
            data,
          });
          expectSuccessfulApiResponse(res);

          const updatedProcess = await getFlowProcess(process.id);
          expect(updatedProcess).toHaveProperty('prevId', null);
        });
      });
    });

    describe('flow process partial update', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const updateDto: PartialUpdateFlowProcessDto = {
          name: 'Updated flow process',
        };
        const res = await axios.request({
          method: 'patch',
          url: '/api/flows/12345/processes/1',
          data: updateDto,
        });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('throws NotFoundException if process does not exist', async () => {
        const updateDto: PartialUpdateFlowProcessDto = {
          name: 'Updated flow process',
        };
        const res = await axios.request({
          method: 'patch',
          url: `/api/flows/${rootFlowId}/processes/12345`,
          data: updateDto,
        });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('updates flow process and returns null', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);

        const updatedDto: PartialUpdateFlowProcessDto = {
          name: 'Updated flow process',
        };
        const res = await axios.request({
          method: 'patch',
          url: `/api/flows/${rootFlowId}/processes/${process.id}`,
          data: updatedDto,
        });
        expectSuccessfulApiResponse(res);
        const { data } = res.data;
        expect(data).toBeNull();
      });

      it('should handle concurrent requests safely when updating flow process (race condition test)', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);

        const responses = await Promise.all(
          Array(3)
            .fill(0)
            .map((_v, index) => {
              const updatedDto: PartialUpdateFlowProcessDto = {
                description: `Updated flow process ${index}`,
              };
              return axios.request({
                method: 'patch',
                url: `/api/flows/${rootFlowId}/processes/${process.id}`,
                data: updatedDto,
              });
            }),
        );

        expect(responses.every(res => (res.status as HttpStatus) === HttpStatus.OK)).toBeTruthy();

        const updatedProcess = await getFlowProcess(process.id);
        expect(['Updated flow process 1', 'Updated flow process 2']).toContain(updatedProcess.description);
      });
    });
  });

  describe('/api/flows/:flowId/processes/:processId/move POST (move flow process)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const res = await axios.request({ method: 'post', url: '/api/flows/abc/processes/1/move' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });

      describe('processId', () => {
        it('throws BadRequestException if processId is not a numerical string', async () => {
          const res = await axios.request({ method: 'post', url: '/api/flows/1/processes/abc/move' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });

      describe('parentId', () => {
        it('throw BadRequestException if parentId is not a numerical string', async () => {
          const moveDto: MoveFlowProcessDto = {
            parentId: 'abc',
            prevId: null,
          };
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes/1/move`,
            data: moveDto,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('throw BadRequestException if parentId is not provided', async () => {
          const moveData = {
            prevId: null,
          } as MoveFlowProcessDto;
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes/1/move`,
            data: moveData,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if parentId is null', async () => {
          const moveDto: MoveFlowProcessDto = {
            parentId: null,
            prevId: null,
          };
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes/1/move`,
            data: moveDto,
          });
          expectSuccessfulApiResponse(res);
        });
      });

      describe('prevId', () => {
        it('throw BadRequestException if prevId is not a numerical string', async () => {
          const moveDto: MoveFlowProcessDto = {
            parentId: null,
            prevId: 'abc',
          };
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes/1/move`,
            data: moveDto,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('throw BadRequestException if prevId is not provided', async () => {
          const moveData = {
            parentId: null,
          } as MoveFlowProcessDto;
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes/1/move`,
            data: moveData,
          });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if prevId is null', async () => {
          const moveDto: MoveFlowProcessDto = {
            parentId: null,
            prevId: null,
          };
          const res = await axios.request({
            method: 'post',
            url: `/api/flows/${rootFlowId}/processes/1/move`,
            data: moveDto,
          });
          expectSuccessfulApiResponse(res);
        });
      });
    });

    describe('flow process movement', () => {
      it('throw NotFoundException if process does not exist', async () => {
        const moveDto: MoveFlowProcessDto = {
          parentId: null,
          prevId: null,
        };
        const res = await axios.request({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes/12345/move`,
          data: moveDto,
        });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'PROCESS_NOT_FOUND');
      });

      it('throw ConflictException if prevId is the same as processId', async () => {
        const moveDto: MoveFlowProcessDto = {
          parentId: null,
          prevId: '1',
        };
        const res = await axios.request({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes/1/move`,
          data: moveDto,
        });
        expectFailedApiResponse(res, HttpStatus.CONFLICT, 'CANNOT_MOVE_TO_ITSELF');
      });

      it('moves flow process and returns null', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);

        const moveDto: MoveFlowProcessDto = {
          parentId: null,
          prevId: null,
        };
        const res = await axios.request({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes/${process.id}/move`,
          data: moveDto,
        });

        expectSuccessfulApiResponse(res);
        const { data } = res.data;
        expect(data).toBeNull();
      });

      it('moves flow process and shift the target process below', async () => {
        // create flow process
        let process1 = await createFlowProcess(createFlowProcessRootDto);

        // create second flow process
        // process2 is above process1 (not it's a target process because it's prevId is null)
        // process2 -> process1
        let process2 = await createFlowProcess(createFlowProcessRootDto);

        process1 = await getFlowProcess(process1.id);
        expect(process1).toHaveProperty('prevId', process2.id);

        const moveDto: MoveFlowProcessDto = {
          parentId: null,
          prevId: null,
        };

        // move process1
        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes/${process1.id}/move`,
          data: moveDto,
        });
        expectSuccessfulApiResponse(res);

        // process1 -> process2
        process1 = await getFlowProcess(process1.id);
        expect(process1).toHaveProperty('prevId', null);

        // check if process2 is below process1
        process2 = await getFlowProcess(process2.id);
        expect(process2).toHaveProperty('prevId', process1.id);
      });

      it('moves flow process and updates prevId the next flow process and shifts the target flow', async () => {
        let process1 = await createFlowProcess(createFlowProcessRootDto);
        let process2 = await createFlowProcess(createFlowProcessRootDto);
        let process3 = await createFlowProcess(createFlowProcessRootDto);
        const process4 = await createFlowProcess(createFlowProcessRootDto);

        // process4 -> process3 -> process2 -> process1

        // move process3 below process2
        const moveDto: MoveFlowProcessDto = {
          parentId: null,
          prevId: process2.id,
        };
        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes/${process3.id}/move`,
          data: moveDto,
        });
        expectSuccessfulApiResponse(res);

        // process4 -> process2 -> process3 -> process1

        // check if process3 is below process2
        process3 = await getFlowProcess(process3.id);
        expect(process3).toHaveProperty('prevId', process2.id);

        // check if process1 is below process3
        process1 = await getFlowProcess(process1.id);
        expect(process1).toHaveProperty('prevId', process3.id);

        // check if process2 is above process3
        process2 = await getFlowProcess(process2.id);
        expect(process2).toHaveProperty('prevId', process4.id);
      });

      it('not move flow process if it is already in the target position', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);

        const moveDto: MoveFlowProcessDto = {
          parentId: null,
          prevId: null,
        };
        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes/${process.id}/move`,
          data: moveDto,
        });
        expectSuccessfulApiResponse(res);

        const updatedProcess = await getFlowProcess(process.id);
        expect(updatedProcess).toHaveProperty('prevId', null);
      });

      it('move flow process to a new parent', async () => {
        const parentProcess1 = await createFlowProcess(createFlowProcessRootDto);
        const parentProcess2 = await createFlowProcess(createFlowProcessRootDto);
        // parentProcess2 -> parentProcess1

        let childProcess1 = await createFlowProcess({
          ...createFlowProcessRootDto,
          parentId: parentProcess1.id,
        });
        let childProcess2 = await createFlowProcess({
          ...createFlowProcessRootDto,
          parentId: parentProcess1.id,
        });
        // parentProcess1: childProcess2 -> childProcess1

        const moveDto: MoveFlowProcessDto = {
          parentId: parentProcess2.id,
          prevId: null,
        };
        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes/${childProcess1.id}/move`,
          data: moveDto,
        });
        expectSuccessfulApiResponse(res);
        // parentProcess1: childProcess2
        // parentProcess2: childProcess1

        childProcess1 = await getFlowProcess(childProcess1.id);
        expect(childProcess1).toHaveProperty('parentId', parentProcess2.id);
        expect(childProcess1).toHaveProperty('prevId', null);

        childProcess2 = await getFlowProcess(childProcess2.id);
        expect(childProcess2).toHaveProperty('parentId', parentProcess1.id);
        expect(childProcess2).toHaveProperty('prevId', null);
      });

      it('move flow process and its children to a new parent', async () => {
        let parentProcessA = await createFlowProcess(createFlowProcessRootDto);
        const parentProcessB = await createFlowProcess(createFlowProcessRootDto);
        // parentProcessB -> parentProcessA

        let childProcessA = await createFlowProcess({
          ...createFlowProcessRootDto,
          parentId: parentProcessA.id,
        });
        let childProcessB = await createFlowProcess({
          ...createFlowProcessRootDto,
          parentId: parentProcessA.id,
        });
        // parentProcessA: childProcessB -> childProcessA
        childProcessA = await getFlowProcess(childProcessA.id);
        childProcessB = await getFlowProcess(childProcessB.id);

        expect(childProcessA.mPath.startsWith(parentProcessA.mPath)).toBeTruthy();
        expect(childProcessB.mPath.startsWith(parentProcessA.mPath)).toBeTruthy();

        const moveDto: MoveFlowProcessDto = {
          parentId: parentProcessB.id,
          prevId: null,
        };
        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'post',
          url: `/api/flows/${rootFlowId}/processes/${parentProcessA.id}/move`,
          data: moveDto,
        });
        expectSuccessfulApiResponse(res);

        // parentProcessB: parentProcessA: childProcessB -> childProcessA

        parentProcessA = await getFlowProcess(parentProcessA.id);
        expect(parentProcessA).toHaveProperty('parentId', parentProcessB.id);
        expect(parentProcessA).toHaveProperty('prevId', null);

        childProcessA = await getFlowProcess(childProcessA.id);
        expect(childProcessA).toHaveProperty('parentId', parentProcessA.id);
        expect(childProcessA).toHaveProperty('prevId', childProcessB.id);
        expect(childProcessA.mPath.startsWith(parentProcessA.mPath)).toBeTruthy();

        childProcessB = await getFlowProcess(childProcessB.id);
        expect(childProcessB).toHaveProperty('parentId', parentProcessA.id);
        expect(childProcessB).toHaveProperty('prevId', null);
        expect(childProcessB.mPath.startsWith(parentProcessA.mPath)).toBeTruthy();
      });

      it('should handle concurrent requests safely when moving flow process (race condition test)', async () => {
        const process = await createFlowProcess(createFlowProcessRootDto);

        const responses = await Promise.all(
          Array(3)
            .fill(0)
            .map(() => {
              const moveDto: MoveFlowProcessDto = {
                parentId: null,
                prevId: null,
              };
              return axios.request<ApiResponsePayload<null>>({
                method: 'post',
                url: `/api/flows/${rootFlowId}/processes/${process.id}/move`,
                data: moveDto,
              });
            }),
        );

        expect(responses.every(res => (res.status as HttpStatus) === HttpStatus.OK)).toBeTruthy();

        const updatedProcess = await getFlowProcess(process.id);
        expect(updatedProcess).toHaveProperty('prevId', null);
      });
    });
  });

  it('tests user work with nested flow process tree', () => {});
});

// TODO: create test for testing deeply nested flow processes
