import { HttpStatus } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { ApiResponsePayload } from '@libs/common/api';
import { FlowModel, CreateFlowDto, MoveFlowDto, UpdateFlowDto, PartialUpdateFlowDto } from '@libs/models';
import { expectFailedApiResponse, expectSuccessfulApiResponse } from '@libs/tests/api';

const createFlow = async (flow: CreateFlowDto): Promise<FlowModel> => {
  const res = await axios.request<ApiResponsePayload<FlowModel>>({
    method: 'post',
    url: '/api/flows',
    data: flow,
  });
  return res.data.data;
};

const deleteFlow = async (flowId: string): Promise<AxiosResponse> => {
  const res = await axios.request({
    method: 'delete',
    url: `/api/flows/${flowId}`,
  });
  return res;
};

const getFlow = async (flowId: string): Promise<FlowModel> => {
  const res = await axios.request<ApiResponsePayload<FlowModel>>({
    method: 'get',
    url: `/api/flows/${flowId}`,
  });
  return res.data.data;
};

/** Check if it is a valid flow */
const expectFlow = (flow: FlowModel, expected: Partial<FlowModel>) => {
  expect(flow).toHaveProperty('id');
  if (expected.id) expect(flow.id).toEqual(expected.id);

  expect(flow).toHaveProperty('name', expected.name);
  expect(flow).toHaveProperty('description', expected.description);
  expect(flow).toHaveProperty('prevId', expected.prevId);

  // assert that there are only 4 properties
  expect(Object.keys(flow)).toHaveLength(4);
};

describe('Flows Module (e2e)', () => {
  describe('/api/flows POST (create flow)', () => {
    describe('validation', () => {
      describe('name', () => {
        it('throw BadRequestException if name is not provided', async () => {
          const data = {
            description: 'Flow description',
            prevId: null,
          } as CreateFlowDto;
          const res = await axios.request({ method: 'post', url: '/api/flows', data });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('throw BadRequestException if name is empty string or less than 2 characters', async () => {
          const data = {
            name: '',
            description: 'Flow description',
            prevId: null,
          } as CreateFlowDto;
          const res = await axios.request({ method: 'post', url: '/api/flows', data });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if name is 2 characters or more', async () => {
          const dto: CreateFlowDto = {
            name: 'AB',
            description: 'Flow description',
            prevId: null,
          };
          const res = await axios.request({ method: 'post', url: '/api/flows', data: dto });
          expect(res.status).toBe(HttpStatus.CREATED);
        });
      });

      describe('description', () => {
        it('throw BadRequestException if description is not provided', async () => {
          const data = {
            name: 'Flow',
            prevId: null,
          } as CreateFlowDto;
          const res = await axios.request({ method: 'post', url: '/api/flows', data: data });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if description is null', async () => {
          const dto: CreateFlowDto = {
            name: 'Flow',
            description: null,
            prevId: null,
          };
          const res = await axios.request({ method: 'post', url: '/api/flows', data: dto });
          expect(res.status).toBe(HttpStatus.CREATED);
        });

        it('should pass if description is empty string and the description should be null in the result', async () => {
          const dto: CreateFlowDto = {
            name: 'Flow',
            description: '',
            prevId: null,
          };

          const res = await axios.request<ApiResponsePayload<CreateFlowDto>>({
            method: 'post',
            url: '/api/flows',
            data: dto,
          });
          expect(res.status).toBe(HttpStatus.CREATED);

          const { data } = res.data;
          expect(data).toHaveProperty('description', null);
        });
      });

      describe('prevId', () => {
        it('throw BadRequestException if prevId is not provided', async () => {
          const data = {
            name: 'Flow',
            description: 'Flow description',
          } as CreateFlowDto;
          const res = await axios.request({ method: 'post', url: '/api/flows', data: data });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('throw BadRequestException if prevId is not a numerical string', async () => {
          const dto: CreateFlowDto = {
            name: 'Flow',
            description: 'Flow description',
            prevId: 'abc',
          };
          const res = await axios.request({ method: 'post', url: '/api/flows', data: dto });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if prevId is null', async () => {
          const dto: CreateFlowDto = {
            name: 'Flow',
            description: 'Flow description',
            prevId: null,
          };
          const res = await axios.request({ method: 'post', url: '/api/flows', data: dto });
          expect(res.status).toBe(HttpStatus.CREATED);
        });
      });
    });

    describe('flow creating', () => {
      it('creates a new flow and returns the created flow', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        const res = await axios.request<ApiResponsePayload<FlowModel>>({
          method: 'post',
          url: '/api/flows',
          data: dto,
        });
        expectSuccessfulApiResponse(res, HttpStatus.CREATED);

        const { data } = res.data;
        expectFlow(data, dto);
      });

      it('creates a new flow and moves the flow with the same prevId (null) below', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        // create first flow with prevId = null
        let flow1 = await createFlow(dto);
        expect(flow1).toHaveProperty('prevId', null);

        // create second flow with prevId = null
        const flow2 = await createFlow(dto);
        expect(flow2).toHaveProperty('prevId', null);

        // get first flow
        flow1 = await getFlow(flow1.id);
        expect(flow1).toHaveProperty('prevId', flow2.id);
      });

      it('creates a new flow and moves the flow with the same prevId (id) below', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        // create first flow with prevId = null
        let flow1 = await createFlow(dto);
        expect(flow1).toHaveProperty('prevId', null);

        // create second flow with prevId = null
        // order flow2 -> flow1
        const flow2 = await createFlow(dto);
        expect(flow2).toHaveProperty('prevId', null);

        // create third flow with prevId = flow2.id
        // order flow2 -> flow3 -> flow1
        const flow3 = await createFlow({ ...dto, prevId: flow2.id });
        expect(flow3).toHaveProperty('prevId', flow2.id);

        // check first flow
        flow1 = await getFlow(flow1.id);
        expect(flow1).toHaveProperty('prevId', flow3.id);
      });

      it('should handle concurrent requests safely (race condition test). flow with prevId = null should be the first one and the rest should be in the same order as they were created', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        const responses = await Promise.all([createFlow(dto), createFlow(dto), createFlow(dto)]);

        expect(responses.every(f => f.prevId === null)).toBeTruthy();

        const flows = await Promise.all(responses.map(f => getFlow(f.id)));

        // check if there is only one flow with prevId = null
        const root = flows.find(f => f.prevId === null);
        expect(root).toBeDefined();

        if (!root) throw new Error('Root not found');

        // check if all flows are in a chain
        const chain = [root];
        let current = flows.find(f => f.prevId === root.id);

        while (current) {
          chain.push(current);
          current = flows.find(f => f.prevId === current?.id);
        }

        // check if all flows are in the chain
        expect(chain.length).toBe(flows.length);
      });
    });
  });

  describe('/api/flows/:flowId DELETE (delete flow)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const res = await axios.request({ method: 'delete', url: '/api/flows/abc' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });
    });

    describe('flow deletion', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const res = await axios.request({ method: 'delete', url: '/api/flows/12345' });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'FLOW_NOT_FOUND');
      });

      it('deletes a flow and returns null', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        // create flow
        const flow = await createFlow(dto);

        // delete flow
        const res = await axios.request<ApiResponsePayload<FlowModel>>({
          method: 'delete',
          url: `/api/flows/${flow.id}`,
        });
        expectSuccessfulApiResponse(res);
        expect(res.data.data).toBeNull();
      });

      it('deletes a flow and moves the next flow above', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        // create first flow with prevId = null
        const flow1 = await createFlow(dto);

        // create second flow with prevId = flow1.id
        let flow2 = await createFlow({ ...dto, prevId: flow1.id });

        // delete first flow
        await axios.request({ method: 'delete', url: `/api/flows/${flow1.id}` });

        // get second flow
        flow2 = await getFlow(flow2.id);
        expect(flow2).toHaveProperty('prevId', null);
      });

      it('should handle concurrent requests safely when deleting a flow (race condition test). First request should delete the flow and the rest should return 404', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        const flow = await createFlow(dto);

        const responses = await Promise.all(
          Array(3)
            .fill(0)
            .map(() => deleteFlow(flow.id)),
        );

        expect(responses[0].status).toBe(HttpStatus.OK);
        expect(responses[1].status).toBe(HttpStatus.NOT_FOUND);
        expect(responses[2].status).toBe(HttpStatus.NOT_FOUND);
      });
    });
  });

  describe('/api/flows/:flowId GET (get flow)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const res = await axios.request({ method: 'get', url: '/api/flows/abc' });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });
    });

    describe('flow retrieval', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const res = await axios.request({ method: 'get', url: '/api/flows/12345' });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'FLOW_NOT_FOUND');
      });

      it('returns a flow', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        // create flow
        const flow = await createFlow(dto);

        const res = await axios.request<ApiResponsePayload<FlowModel>>({
          method: 'get',
          url: `/api/flows/${flow.id}`,
        });
        expectSuccessfulApiResponse(res);
        expectFlow(res.data.data, flow);
      });
    });
  });

  describe('/api/flows GET (get all flows)', () => {
    it('returns all flows', async () => {
      const dto: CreateFlowDto = {
        name: 'Flow',
        description: 'Flow description',
        prevId: null,
      };

      // create flow
      const flow = await createFlow(dto);

      const res = await axios.request<ApiResponsePayload<FlowModel[]>>({
        method: 'get',
        url: '/api/flows',
      });
      expectSuccessfulApiResponse(res);
      expect(res.data.data).toBeInstanceOf(Array);
      // find the created flow
      expect(res.data.data.find(f => f.id === flow.id)).toBeDefined();
    });
  });

  describe('/api/flows/:flowId PUT (update flow)', () => {
    describe('validation', () => {
      // !!!
      // tests below rely on the fact that there is a flow with id = 1
      describe('name', () => {
        it('throw BadRequestException if name is not provided', async () => {
          const data = {
            description: 'Updated description',
          } as UpdateFlowDto;
          const res = await axios.request({ method: 'put', url: '/api/flows/1', data });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('throw BadRequestException if name is empty string or less than 2 characters', async () => {
          const dto: UpdateFlowDto = {
            name: '',
            description: 'Updated description',
          };
          const res = await axios.request({ method: 'put', url: '/api/flows/1', data: dto });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if name is 2 characters or more', async () => {
          const dto: UpdateFlowDto = {
            name: 'AB',
            description: 'Updated description',
          };
          const res = await axios.request({ method: 'put', url: '/api/flows/1', data: dto });
          expect(res.status).toBe(HttpStatus.OK);
        });
      });

      describe('description', () => {
        it('throw BadRequestException if description is not provided', async () => {
          const data = {
            name: 'Updated flow',
          } as UpdateFlowDto;
          const res = await axios.request({ method: 'put', url: '/api/flows/1', data });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if description is null', async () => {
          const dto: UpdateFlowDto = {
            name: 'Updated flow',
            description: null,
          };
          const res = await axios.request({ method: 'put', url: '/api/flows/1', data: dto });
          expect(res.status).toBe(HttpStatus.OK);
        });

        it('should pass if description is empty string and the description should be null in the result', async () => {
          const dto: UpdateFlowDto = {
            name: 'Updated flow',
            description: '',
          };
          const res = await axios.request<ApiResponsePayload<FlowModel>>({
            method: 'put',
            url: '/api/flows/1',
            data: dto,
          });
          expect(res.status).toBe(HttpStatus.OK);
          const { data } = res.data;
          expect(data).toHaveProperty('description', null);
        });
      });

      describe('prevId', () => {
        it('should not update prevId if prevId is provided', async () => {
          const dto: CreateFlowDto = {
            name: 'Flow',
            description: 'Flow description',
            prevId: '1',
          };

          const flow = await createFlow(dto);

          const updatedData = {
            name: 'Updated flow',
            description: 'Updated description',
            prevId: null,
          };
          const res = await axios.request({ method: 'put', url: `/api/flows/${flow.id}`, data: updatedData });
          expect(res.status).toBe(HttpStatus.OK);

          const updatedFlow = await getFlow(flow.id);
          expect(updatedFlow).toHaveProperty('prevId', flow.prevId);
        });
      });

      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const dto: UpdateFlowDto = {
            name: 'Updated flow',
            description: 'Updated description',
          };
          const res = await axios.request({ method: 'put', url: '/api/flows/abc', data: dto });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });
    });

    describe('flow update', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const dto: UpdateFlowDto = {
          name: 'Updated flow',
          description: 'Updated description',
        };
        const res = await axios.request({ method: 'put', url: '/api/flows/12345', data: dto });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'FLOW_NOT_FOUND');
      });

      it('updates a flow and returns the updated flow', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        // create flow
        const flow = await createFlow(dto);

        const updatedDto: UpdateFlowDto = {
          name: 'Updated flow',
          description: 'Updated description',
        };

        // update flow
        const res = await axios.request<ApiResponsePayload<FlowModel>>({
          method: 'put',
          url: `/api/flows/${flow.id}`,
          data: updatedDto,
        });
        expectSuccessfulApiResponse(res);

        const { data } = res.data;
        expectFlow(data, { ...flow, ...updatedDto });
      });

      it('should handle concurrent requests safely when updating a flow (race condition test)', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        const flow = await createFlow(dto);

        const responses = await Promise.all(
          Array(3)
            .fill(0)
            .map((_v, index) => {
              const updatedDto: UpdateFlowDto = {
                name: 'Updated flow',
                description: `Updated flow ${index}`,
              };
              return axios.request({ method: 'put', url: `/api/flows/${flow.id}`, data: updatedDto });
            }),
        );

        expect(responses.every(r => (r.status as HttpStatus) === HttpStatus.OK)).toBeTruthy();

        // check if the second or third update was applied
        const updatedFlow = await getFlow(flow.id);
        expect(['Updated flow 1', 'Updated flow 2']).toContain(updatedFlow.description);
      });
    });
  });

  describe('/api/flows/:flowId PATCH (partial update flow)', () => {
    describe('validation', () => {
      // !!!
      // tests below rely on the fact that there is a flow with id = 1
      describe('name', () => {
        it('should pass if name is not provided', async () => {
          const dto: PartialUpdateFlowDto = {
            description: 'Updated description',
          };
          const res = await axios.request({ method: 'patch', url: '/api/flows/1', data: dto });
          expect(res.status).toBe(HttpStatus.OK);
        });

        it('throw BadRequestException if name is empty string or less than 2 characters', async () => {
          const dto: PartialUpdateFlowDto = {
            name: 'A',
          };
          const res = await axios.request({ method: 'patch', url: '/api/flows/1', data: dto });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if name is 2 characters or more', async () => {
          const dto: PartialUpdateFlowDto = {
            name: 'AB',
          };
          const res = await axios.request({ method: 'patch', url: '/api/flows/1', data: dto });
          expect(res.status).toBe(HttpStatus.OK);
        });
      });

      describe('description', () => {
        it('should pass if description is not provided', async () => {
          const dto: PartialUpdateFlowDto = {
            name: 'Updated flow',
          };
          const res = await axios.request({ method: 'patch', url: '/api/flows/1', data: dto });
          expect(res.status).toBe(HttpStatus.OK);
        });

        it('should pass if description is null', async () => {
          const dto: PartialUpdateFlowDto = {
            description: null,
          };
          const res = await axios.request({ method: 'patch', url: '/api/flows/1', data: dto });
          expect(res.status).toBe(HttpStatus.OK);
        });

        it('should pass if description is empty string and the description should be null in the result', async () => {
          const dto: PartialUpdateFlowDto = {
            description: '',
          };
          await axios.request({ method: 'patch', url: '/api/flows/1', data: dto });
          const flow = await getFlow('1');
          expect(flow).toHaveProperty('description', null);
        });
      });

      describe('prevId', () => {
        it('should not update prevId if prevId is provided', async () => {
          const dto: CreateFlowDto = {
            name: 'Flow',
            description: 'Flow description',
            prevId: '1',
          };

          const flow = await createFlow(dto);

          const data = { prevId: null };
          const res = await axios.request({ method: 'patch', url: `/api/flows/${flow.id}`, data });
          expect(res.status).toBe(HttpStatus.OK);

          const updatedFlow = await getFlow(flow.id);
          expect(updatedFlow).toHaveProperty('prevId', flow.prevId);
        });
      });

      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const dto: PartialUpdateFlowDto = {
            name: 'Updated flow',
          };
          const res = await axios.request({ method: 'patch', url: '/api/flows/abc', data: dto });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });
    });

    describe('flow partial update', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const dto: PartialUpdateFlowDto = {
          name: 'Updated flow',
        };
        const res = await axios.request({ method: 'patch', url: '/api/flows/12345', data: dto });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'FLOW_NOT_FOUND');
      });

      it('updates a flow and returns null', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        // create flow
        const flow = await createFlow(dto);

        // update flow
        const updatedDto: PartialUpdateFlowDto = {
          name: 'Updated flow',
          description: 'Updated description',
        };
        const res = await axios.request({ method: 'patch', url: `/api/flows/${flow.id}`, data: updatedDto });
        expectSuccessfulApiResponse(res);
        expect(res.data.data).toBeNull();
      });

      it('should handle concurrent requests safely when updating a flow (race condition test)', async () => {
        const dto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        const flow = await createFlow(dto);

        const responses = await Promise.all(
          Array(3)
            .fill(0)
            .map((_v, index) => {
              const updatedDto: PartialUpdateFlowDto = {
                description: `Updated flow ${index}`,
              };
              return axios.request({ method: 'patch', url: `/api/flows/${flow.id}`, data: updatedDto });
            }),
        );

        expect(responses.every(res => (res.status as HttpStatus) === HttpStatus.OK)).toBeTruthy();

        const updatedFlow = await getFlow(flow.id);
        expect(['Updated flow 1', 'Updated flow 2']).toContain(updatedFlow.description);
      });
    });
  });

  describe('/api/flows/:flowId/move POST (move flow)', () => {
    describe('validation', () => {
      describe('flowId', () => {
        it('throws BadRequestException if flowId is not a numerical string', async () => {
          const dto: MoveFlowDto = {
            prevId: '1',
          };
          const res = await axios.request({ method: 'post', url: '/api/flows/abc/move', data: dto });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });
      });

      describe('prevId', () => {
        it('throw BadRequestException if prevId is not provided', async () => {
          const res = await axios.request({ method: 'post', url: '/api/flows/1/move', data: {} });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('throw BadRequestException if prevId is not a numerical string', async () => {
          const dto: MoveFlowDto = {
            prevId: 'abc',
          };
          const res = await axios.request({ method: 'post', url: '/api/flows/1/move', data: dto });
          expectFailedApiResponse(res, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
        });

        it('should pass if prevId is null', async () => {
          const dto: MoveFlowDto = {
            prevId: null,
          };
          const res = await axios.request({ method: 'post', url: '/api/flows/1/move', data: dto });
          expectSuccessfulApiResponse(res);
        });
      });
    });

    describe('flow movement', () => {
      it('throws NotFoundException if flow does not exist', async () => {
        const dto: MoveFlowDto = {
          prevId: null,
        };
        const res = await axios.request({ method: 'post', url: '/api/flows/12345/move', data: dto });
        expectFailedApiResponse(res, HttpStatus.NOT_FOUND, 'FLOW_NOT_FOUND');
      });

      it('throw ConflictException if prevId is the same as flowId', async () => {
        const dto: MoveFlowDto = {
          prevId: '1',
        };
        const res = await axios.request({ method: 'post', url: '/api/flows/1/move', data: dto });
        expectFailedApiResponse(res, HttpStatus.CONFLICT, 'CANNOT_MOVE_TO_ITSELF');
      });

      it('moves a flow and returns null', async () => {
        const flowDto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        const moveDto: MoveFlowDto = {
          prevId: null,
        };

        const flow = await createFlow(flowDto);

        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'post',
          url: `/api/flows/${flow.id}/move`,
          data: moveDto,
        });
        expectSuccessfulApiResponse(res);
        expect(res.data.data).toBeNull();
      });

      it('moves a flow and shifts the target flow below', async () => {
        const flowDto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        // create flow
        let flow1 = await createFlow(flowDto);

        // create second flow
        // flow2 is above flow1 (not it's a target flow because it's prevId is null)
        let flow2 = await createFlow(flowDto);

        flow1 = await getFlow(flow1.id);
        expect(flow1).toHaveProperty('prevId', flow2.id);

        const moveDto: MoveFlowDto = {
          prevId: null,
        };

        // move flow1
        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'post',
          url: `/api/flows/${flow1.id}/move`,
          data: moveDto,
        });
        expectSuccessfulApiResponse(res);

        // check if flow1 is moved to the top
        flow1 = await getFlow(flow1.id);
        expect(flow1).toHaveProperty('prevId', null);

        // check if flow2 is below flow1
        flow2 = await getFlow(flow2.id);
        expect(flow2).toHaveProperty('prevId', flow1.id);
      });

      it('moves a flow, updates prevId the next flow and shifts the target flow', async () => {
        const flowDto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        let flow1 = await createFlow(flowDto);
        let flow2 = await createFlow(flowDto);
        let flow3 = await createFlow(flowDto);
        const flow4 = await createFlow(flowDto);

        // flow4(prevId = null) -> flow3(prevId = flow4.id) -> flow2(prevId = flow3.id) -> flow1(prevId = flow2.id)

        // move flow3 below flow2
        const moveDto: MoveFlowDto = {
          prevId: flow2.id,
        };
        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'post',
          url: `/api/flows/${flow3.id}/move`,
          data: moveDto,
        });
        expectSuccessfulApiResponse(res);

        // flow4 -> flow2 -> flow3 -> flow1

        // check if flow3 is below flow2
        flow3 = await getFlow(flow3.id);
        expect(flow3).toHaveProperty('prevId', flow2.id);

        // check if flow1 is below flow3
        flow1 = await getFlow(flow1.id);
        expect(flow1).toHaveProperty('prevId', flow3.id);

        // check if flow2 is above flow3
        flow2 = await getFlow(flow2.id);
        expect(flow2).toHaveProperty('prevId', flow4.id);
      });

      it('should not move a flow if it is already in the target position', async () => {
        const flowDto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        const flow = await createFlow(flowDto);

        const moveDto: MoveFlowDto = {
          prevId: null,
        };

        const res = await axios.request<ApiResponsePayload<null>>({
          method: 'post',
          url: `/api/flows/${flow.id}/move`,
          data: moveDto,
        });
        expectSuccessfulApiResponse(res);

        const updatedFlow = await getFlow(flow.id);
        expect(updatedFlow).toHaveProperty('prevId', null);
      });

      it('should handle concurrent requests safely when moving a flow (race condition test)', async () => {
        const flowDto: CreateFlowDto = {
          name: 'Flow',
          description: 'Flow description',
          prevId: null,
        };

        const flow = await createFlow(flowDto);

        const responses = await Promise.all(
          Array(3)
            .fill(0)
            .map(() => {
              const moveDto: MoveFlowDto = {
                prevId: null,
              };
              return axios.request<ApiResponsePayload<null>>({
                method: 'post',
                url: `/api/flows/${flow.id}/move`,
                data: moveDto,
              });
            }),
        );

        expect(responses.every(res => (res.status as HttpStatus) === HttpStatus.OK)).toBeTruthy();

        const updatedFlow = await getFlow(flow.id);
        expect(updatedFlow).toHaveProperty('prevId', null);
      });
    });
  });
});
