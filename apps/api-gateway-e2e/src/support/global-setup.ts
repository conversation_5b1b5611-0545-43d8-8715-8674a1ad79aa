import dotenv from 'dotenv';
import fs from 'node:fs';
import { resolve } from 'node:path';
import { PgDbManager } from '@libs/tests/postgres';
import { waitForPortOpen } from '@nx/node/utils';

module.exports = async function () {
  // Start services that that the app needs to run (e.g. database, docker-compose, etc.).
  // eslint-disable-next-line no-console
  console.log('\nSetting up...\n');

  // Init scrub-hub
  const scrumHubEnv = dotenv.parse(fs.readFileSync(resolve('./apps/scrum-hub/.env.staging')));
  globalThis.__SCRUM_HUB_ENV__ = scrumHubEnv;

  globalThis.__SCRUM_HUB_PG_CONFIG__ = {
    host: scrumHubEnv.PG_HOST,
    port: parseInt(scrumHubEnv.PG_PORT || '5432'),
    user: scrumHubEnv.PG_USER,
    password: scrumHubEnv.PG_PASSWORD,
    database: scrumHubEnv.PG_DB,
  };

  // Prepare scrum-hub database
  const pgDbManager = new PgDbManager(globalThis.__SCRUM_HUB_PG_CONFIG__);
  await pgDbManager.createDb();

  // Wait for api-gateway app to be ready
  const scrumHubHost = scrumHubEnv.HOST ?? 'localhost';
  const scrumHubPort = scrumHubEnv.PORT ? Number(scrumHubEnv.PORT) : 3001;
  await waitForPortOpen(scrumHubPort, { host: scrumHubHost });

  // Init api-gateway
  const apiGatewayEnv = dotenv.parse(fs.readFileSync(resolve('./apps/api-gateway/.env.staging')));
  globalThis.__API_GATEWAY_ENV__ = apiGatewayEnv;

  // Wait for api-gateway app to be ready
  const apiGatewayHost = apiGatewayEnv.HOST ?? 'localhost';
  const apiGatewayPort = apiGatewayEnv.PORT ? Number(apiGatewayEnv.PORT) : 3000;
  await waitForPortOpen(apiGatewayPort, { host: apiGatewayHost });

  // Hint: Use `globalThis` to pass variables to global teardown.
  globalThis.__TEARDOWN_MESSAGE__ = '\nTearing down...\n';
};
