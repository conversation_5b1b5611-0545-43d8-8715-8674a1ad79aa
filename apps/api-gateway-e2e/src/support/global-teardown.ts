import { PgDbManager } from '@libs/tests/postgres';
import { killPort } from '@nx/node/utils';

module.exports = async function () {
  // Put clean up logic here (e.g. stopping services, docker-compose, etc.).

  const scrumHubEnv = globalThis.__SCRUM_HUB_ENV__;

  const pgDbManager = new PgDbManager(globalThis.__SCRUM_HUB_PG_CONFIG__);
  await pgDbManager.deleteDb();

  // Hint: `globalThis` is shared between setup and teardown.
  const scrumHubPort = scrumHubEnv.PORT ? Number(scrumHubEnv.PORT) : 3001;

  const apiGatewayEnv = globalThis.__API_GATEWAY_ENV__;
  const apiGatewayPort = apiGatewayEnv.PORT ? Number(apiGatewayEnv.PORT) : 3000;

  await Promise.all([killPort(scrumHubPort), killPort(apiGatewayPort)]);

  // eslint-disable-next-line no-console
  console.log(globalThis.__TEARDOWN_MESSAGE__);
};
