{
  "extends": "../../tsconfig.base.json",
  "files": [],
  "include": [],
  "references": [
    {
      "path": "./tsconfig.spec.json"
    }
  ],
  "compilerOptions": {
    "esModuleInterop": true
    // "baseUrl": ".",
    // "paths": {
    //   "@libs/common/api": ["../../libs/common/src/api/index.ts"],
    //   "@libs/models": ["../../libs/models/src/index.ts"],
    //   "@libs/tests/api": ["../../libs/tests/src/api.ts"],
    //   "@libs/tests/postgres": ["../../libs/tests/src/postgres.ts"]
    // }
  }
}
