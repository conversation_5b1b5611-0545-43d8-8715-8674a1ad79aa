{"name": "@apps/api-gateway", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api-gateway/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["--node-env=production"], "cwd": "apps/api-gateway"}, "configurations": {"development": {"args": ["--node-env=development"]}, "staging": {"args": ["--node-env=staging"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build", "@apps/scrum-hub:serve"], "options": {"buildTarget": "@apps/api-gateway:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@apps/api-gateway:build:development"}, "production": {"buildTarget": "@apps/api-gateway:build:production"}}}, "serve-staging": {"continuous": true, "parallel": true, "executor": "@nx/js:node", "defaultConfiguration": "staging", "options": {"buildTarget": "@apps/api-gateway:build", "runBuildTargetDependencies": false}}, "test": {"options": {"passWithNoTests": true}}}}