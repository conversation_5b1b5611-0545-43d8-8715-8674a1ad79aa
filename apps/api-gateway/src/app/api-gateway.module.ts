import { Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';
import { GlobalApiExceptionFilter, ZodResponseInterceptor } from '@libs/common/api';
import { ConfigModule, ConfigService } from '@libs/common/config';
import { usePinoHttpOptions, LoggerModule } from '@libs/pino-logger';
import { EnvironmentVariables, EnvVariablesSchema } from '../config';
import { FlowProcessesController } from '../controllers/flow-processes.controller';
import { FlowsController } from '../controllers/flows.controller';
import { SCRUM_HUB } from './api-gateway.constants';

@Module({
  imports: [
    ConfigModule.forRoot({
      schema: EnvVariablesSchema,
    }),

    LoggerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService<EnvironmentVariables>) => {
        const pinoHttp = usePinoHttpOptions({
          configService: config,
          appName: config.get('LOGSTASH_APP_NAME') as string,
        });
        return { pinoHttp };
      },
    }),

    ClientsModule.registerAsync([
      {
        name: SCRUM_HUB,
        inject: [ConfigService],
        useFactory: async (config: ConfigService<EnvironmentVariables>) => ({
          transport: Transport.TCP,
          options: {
            host: config.get('SCRUM_HUB_HOST'),
            port: config.get('SCRUM_HUB_PORT'),
          },
        }),
      },
    ]),
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodResponseInterceptor,
    },
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalApiExceptionFilter,
    },
  ],

  controllers: [FlowsController, FlowProcessesController],
})
export class ApiGatewayModule {}
