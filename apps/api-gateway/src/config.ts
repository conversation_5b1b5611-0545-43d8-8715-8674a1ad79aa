import { z } from 'zod';

export const EnvVariablesSchema = z.object({
  PORT: z.coerce.number().default(3000),

  // Logger configuration
  LOG_LEVEL: z.string().default('trace'),

  // Elasticsearch/Logstash configuration
  LOGSTASH_APP_NAME: z.string().default('easy-flow--api-gateway'),
  LOGSTASH_HOST: z.string().optional(),
  LOGSTASH_PORT: z.coerce.number().default(50000),

  // Swagger configuration
  SWAGGER_ENABLED: z.coerce.boolean().default(false),

  // Microservices configuration
  SCRUM_HUB_HOST: z.string(),
  SCRUM_HUB_PORT: z.coerce.number(),
});

export type EnvironmentVariables = z.infer<typeof EnvVariablesSchema>;
