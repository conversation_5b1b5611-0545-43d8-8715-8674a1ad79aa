import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  Patch,
  Post,
  Put,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ApiOperation, ApiParam } from '@nestjs/swagger';
import { firstValueFrom } from 'rxjs';
import { EMPTY_RESPONSE, HttpResponse, SanitizeResponseWithZod } from '@libs/common/api';
import { NumericIdPipe } from '@libs/common/database';
import { ApiErrorResponse, ApiSuccessResponse } from '@libs/common/swagger';
import {
  FlowProcessSchema,
  FlowProcessDto,
  CreateFlowProcessDto,
  MoveFlowProcessDto,
  UpdateFlowProcessDto,
  PartialUpdateFlowProcessDto,
  type CreateFlowProcessPayload,
  type UpdateFlowProcessPayload,
  type PartialUpdateFlowProcessPayload,
  type DeleteFlowProcessPayload,
  type MoveFlowProcessPayload,
  type ReadFlowProcessByIdPayload,
  type ReadAllFlowProcessesPayload,
} from '@libs/models';
import { SCRUM_HUB } from '../app/api-gateway.constants';

const ApiParamFlowProcessId = () =>
  ApiParam({ name: 'processId', required: true, description: 'Process identifier', type: 'string' });

@Controller('flows/:flowId/processes')
@ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' })
export class FlowProcessesController {
  constructor(@Inject(SCRUM_HUB) private scrumHub: ClientProxy) {}

  @Post()
  @ApiOperation({ summary: 'Create process' })
  @ApiSuccessResponse(HttpStatus.CREATED, 'Process created', FlowProcessDto)
  @ApiErrorResponse(HttpStatus.CONFLICT, 'Parent process not found', 'PARENT_PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessSchema)
  async createProcess(@Param('flowId', NumericIdPipe) flowId: string, @Body() dto: CreateFlowProcessDto) {
    const payload: CreateFlowProcessPayload = { flowId, dto };
    const process = await firstValueFrom(this.scrumHub.send('flow-processes: create flow process', payload));
    return new HttpResponse({ statusCode: HttpStatus.CREATED, data: process, message: 'Process created' });
  }

  @Put(':processId')
  @ApiOperation({ summary: 'Update process' })
  @ApiParamFlowProcessId()
  @ApiSuccessResponse(HttpStatus.OK, 'Process updated', FlowProcessDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessSchema)
  async updateProcess(
    @Param('flowId', NumericIdPipe) flowId: string,
    @Param('processId', NumericIdPipe) processId: string,
    @Body() dto: UpdateFlowProcessDto,
  ) {
    const payload: UpdateFlowProcessPayload = { flowId, processId, dto };
    const process = await firstValueFrom(this.scrumHub.send('flow-processes: update flow process', payload));
    return new HttpResponse({ data: process, message: 'Process updated' });
  }

  @Patch(':processId')
  @ApiOperation({ summary: 'Partial update process' })
  @ApiParamFlowProcessId()
  @ApiSuccessResponse(HttpStatus.OK, 'Process updated', FlowProcessDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async partialUpdateProcess(
    @Param('flowId', NumericIdPipe) flowId: string,
    @Param('processId', NumericIdPipe) processId: string,
    @Body() dto: PartialUpdateFlowProcessDto,
  ) {
    const payload: PartialUpdateFlowProcessPayload = { flowId, processId, dto };
    await firstValueFrom(this.scrumHub.send('flow-processes: update flow process', payload));
    return new HttpResponse({ message: 'Process updated' });
  }

  @Delete(':processId')
  @ApiOperation({ summary: 'Delete process' })
  @ApiParamFlowProcessId()
  @ApiSuccessResponse(HttpStatus.OK, 'Process deleted', FlowProcessDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async deleteProcess(
    @Param('flowId', NumericIdPipe) flowId: string,
    @Param('processId', NumericIdPipe) processId: string,
  ) {
    const payload: DeleteFlowProcessPayload = { flowId, processId };
    await firstValueFrom(this.scrumHub.send('flow-processes: delete flow process', payload));
    return new HttpResponse({ message: 'Process deleted' });
  }

  @Post(':processId/move')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Move process' })
  @ApiParamFlowProcessId()
  @ApiSuccessResponse(HttpStatus.OK, 'Process moved', FlowProcessDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async moveProcess(
    @Param('flowId', NumericIdPipe) flowId: string,
    @Param('processId', NumericIdPipe) processId: string,
    @Body() dto: MoveFlowProcessDto,
  ) {
    const payload: MoveFlowProcessPayload = { processId, flowId, dto };
    await firstValueFrom(this.scrumHub.send('flow-processes: move flow process', payload));
    return new HttpResponse({ message: 'Process moved' });
  }

  @Get(':processId')
  @ApiOperation({ summary: 'Get process' })
  @ApiParamFlowProcessId()
  @ApiSuccessResponse(HttpStatus.OK, 'Process found', FlowProcessDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessSchema)
  async getProcess(
    @Param('flowId', NumericIdPipe) flowId: string,
    @Param('processId', NumericIdPipe) processId: string,
  ) {
    const payload: ReadFlowProcessByIdPayload = { flowId, processId };
    const process = await firstValueFrom(
      this.scrumHub.send('flow-processes: get flow process by id', payload),
    );
    return new HttpResponse({ data: process, message: 'Process found' });
  }

  @Get()
  @ApiOperation({ summary: 'Get all flow processes' })
  @ApiSuccessResponse(HttpStatus.OK, 'Flow processes found', [FlowProcessDto])
  @SanitizeResponseWithZod(FlowProcessSchema.array())
  async getProcesses(@Param('flowId', NumericIdPipe) flowId: string) {
    const payload: ReadAllFlowProcessesPayload = { flowId };
    const processes = await firstValueFrom(
      this.scrumHub.send('flow-processes: get all flow processes', payload),
    );
    return new HttpResponse({ data: processes, message: 'Flow processes found' });
  }
}
