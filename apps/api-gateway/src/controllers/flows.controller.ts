import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  Patch,
  Post,
  Put,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ApiOperation, ApiParam } from '@nestjs/swagger';
import { firstValueFrom } from 'rxjs';
import { EMPTY_RESPONSE, HttpResponse, SanitizeResponseWithZod } from '@libs/common/api';
import { NumericIdPipe } from '@libs/common/database';
import { ApiErrorResponse, ApiSuccessResponse } from '@libs/common/swagger';
import {
  CreateFlowDto,
  FlowDto,
  MoveFlowDto,
  PartialUpdateFlowDto,
  UpdateFlowDto,
  FlowSchema,
  type CreateFlowPayload,
  type ReadFlowByIdPayload,
  type UpdateFlowPayload,
  type PartialUpdateFlowPayload,
  type DeleteFlowPayload,
  type MoveFlowPayload,
} from '@libs/models';
import { SCRUM_HUB } from '../app/api-gateway.constants';

const ApiParamFlowId = () =>
  ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' });

@Controller('flows')
export class FlowsController {
  constructor(@Inject(SCRUM_HUB) private scrumHub: ClientProxy) {}

  @Post()
  @ApiOperation({ summary: 'Create flow' })
  @ApiSuccessResponse(HttpStatus.CREATED, 'Flow created', FlowDto)
  @SanitizeResponseWithZod(FlowSchema)
  async createFlow(@Body() dto: CreateFlowDto) {
    const payload: CreateFlowPayload = { dto };
    const flow = await firstValueFrom(this.scrumHub.send('flows: create flow', payload));
    return new HttpResponse({ statusCode: HttpStatus.CREATED, data: flow, message: 'Flow created' });
  }

  @Put(':flowId')
  @ApiOperation({ summary: 'Update flow' })
  @ApiParamFlowId()
  @ApiSuccessResponse(HttpStatus.OK, 'Flow updated', FlowDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(FlowSchema)
  async updateFlow(@Param('flowId', NumericIdPipe) flowId: string, @Body() dto: UpdateFlowDto) {
    const payload: UpdateFlowPayload = { flowId, dto };
    const flow = await firstValueFrom(this.scrumHub.send('flows: update flow', payload));
    return new HttpResponse({ data: flow, message: 'Flow updated' });
  }

  @Patch(':flowId')
  @ApiOperation({ summary: 'Partial update flow' })
  @ApiParamFlowId()
  @ApiSuccessResponse(HttpStatus.OK, 'Flow updated', FlowDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async partialUpdateFlow(@Param('flowId', NumericIdPipe) flowId: string, @Body() dto: PartialUpdateFlowDto) {
    const payload: PartialUpdateFlowPayload = { flowId, dto };
    await firstValueFrom(this.scrumHub.send('flows: update flow', payload));
    return new HttpResponse({ message: 'Flow partially updated' });
  }

  @Delete(':flowId')
  @ApiOperation({ summary: 'Delete flow' })
  @ApiParamFlowId()
  @ApiSuccessResponse(HttpStatus.OK, 'Flow deleted', FlowDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async deleteFlow(@Param('flowId', NumericIdPipe) flowId: string) {
    const payload: DeleteFlowPayload = { flowId };
    await firstValueFrom(this.scrumHub.send('flows: delete flow', payload));
    return new HttpResponse({ message: 'Flow deleted' });
  }

  @Post(':flowId/move')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Move flow' })
  @ApiParamFlowId()
  @ApiSuccessResponse(HttpStatus.OK, 'Flow moved', FlowDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async moveFlow(@Param('flowId', NumericIdPipe) flowId: string, @Body() dto: MoveFlowDto) {
    const payload: MoveFlowPayload = { flowId, dto };
    await firstValueFrom(this.scrumHub.send('flows: move flow', payload));
    return new HttpResponse({ message: 'Flow moved' });
  }

  @Get(':flowId')
  @ApiOperation({ summary: 'Get flow' })
  @ApiParamFlowId()
  @ApiSuccessResponse(HttpStatus.OK, 'Flow found', FlowDto)
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(FlowSchema)
  async getFlow(@Param('flowId', NumericIdPipe) flowId: string) {
    const payload: ReadFlowByIdPayload = { flowId };
    const flow = await firstValueFrom(this.scrumHub.send('flows: get flow by id', payload));
    return new HttpResponse({ data: flow, message: 'Flow found' });
  }

  @Get()
  @ApiOperation({ summary: 'Get all flows' })
  @ApiSuccessResponse(HttpStatus.OK, 'Flows found', [FlowDto])
  @SanitizeResponseWithZod(FlowSchema.array())
  async getAllFlows() {
    const flows = await firstValueFrom(this.scrumHub.send('flows: get all flows', {}));
    return new HttpResponse({ data: flows, message: 'Flows found' });
  }
}
