import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { patchNestJsSwagger } from 'nestjs-zod';
import { ConfigService } from '@libs/common/config';
import { Logger, LoggerErrorInterceptor } from '@libs/pino-logger';
import { ApiGatewayModule } from './app/api-gateway.module';

patchNestJsSwagger();

async function bootstrap() {
  const app = await NestFactory.create(ApiGatewayModule);

  const config = app.get(ConfigService);

  // TODO
  // app.enableShutdownHooks();

  app.setGlobalPrefix('/api');

  // Configure logger
  const logger = app.get(Logger);
  app.useLogger(logger);
  app.useGlobalInterceptors(new LoggerErrorInterceptor());

  // Swagger setup
  if (config.get('SWAGGER_ENABLED')) {
    const openApiConfig = new DocumentBuilder()
      .setTitle('API')
      .setVersion('0.0.0')
      // .addBearerAuth({ type: 'http', scheme: 'bearer' }, ACCESS_TOKEN_SECURITY_KEY)
      .build();

    const openApi = SwaggerModule.createDocument(app, openApiConfig);
    // to see the webpage open: /swagger
    SwaggerModule.setup('swagger', app, openApi, {
      jsonDocumentUrl: 'swagger/json',
    });
  }

  await app.listen(config.getOrThrow('PORT'));

  logger.log(`
    🚀 Server started on port ${await app.getUrl()}
    Swagger: ${await app.getUrl()}/swagger
    NODE_ENV: ${process.env.NODE_ENV}
  `);
}

void bootstrap();
