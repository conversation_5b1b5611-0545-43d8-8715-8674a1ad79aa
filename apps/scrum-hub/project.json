{"name": "@apps/scrum-hub", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/scrum-hub/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["--node-env=production"], "cwd": "apps/scrum-hub"}, "configurations": {"development": {"args": ["--node-env=development"]}, "staging": {"args": ["--node-env=staging"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "@apps/scrum-hub:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@apps/scrum-hub:build:development"}, "production": {"buildTarget": "@apps/scrum-hub:build:production"}}}, "serve-staging": {"continuous": true, "parallel": true, "executor": "@nx/js:node", "defaultConfiguration": "staging", "options": {"buildTarget": "@apps/scrum-hub:build", "runBuildTargetDependencies": false}}, "test": {"options": {"passWithNoTests": true}}}}