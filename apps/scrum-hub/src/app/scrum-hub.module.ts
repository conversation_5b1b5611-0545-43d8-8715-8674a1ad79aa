import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { EnvironmentVariables, EnvVariablesSchema } from '@/config';
import { HttpToRpcExceptionFilter } from '@libs/common/api';
import { ConfigModule, ConfigService } from '@libs/common/config';
import { LoggerModule, usePinoHttpOptions } from '@libs/pino-logger';
import { RedisModule } from '@libs/redis';
import { FlowProcessesModule } from '../modules/flows_flow-processes/flow-processes.module';
import { FlowsModule } from '../modules/flows_flows/flows.module';
import { ScrumHubController } from './scrum-hub.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      schema: EnvVariablesSchema,
    }),

    LoggerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService) => {
        const pinoHttp = usePinoHttpOptions({
          configService: config,
          appName: config.getOrThrow('LOGSTASH_APP_NAME'),
        });
        return { pinoHttp };
      },
    }),

    RedisModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (config: ConfigService<EnvironmentVariables>) => ({
        host: config.get('REDIS_HOST'),
        port: config.get('REDIS_PORT'),
      }),
    }),

    FlowsModule,
    FlowProcessesModule,
    // FlowProcessStepsModule,
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: HttpToRpcExceptionFilter,
    },
  ],
  controllers: [ScrumHubController],
})
export class ScrumHubModule {}
