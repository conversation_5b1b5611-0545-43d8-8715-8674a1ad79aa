import { z } from 'zod';

// .env
export const EnvVariablesSchema = z.object({
  // PostgreSQL
  PG_USER: z.string(),
  PG_PASSWORD: z.string(),
  PG_DB: z.string(),
  PG_HOST: z.string(),
  PG_PORT: z.coerce.number(),

  // Redis
  REDIS_HOST: z.string(),
  REDIS_PORT: z.coerce.number(),

  // Logger configuration
  LOG_LEVEL: z.string().default('trace'),

  // Elasticsearch/Logstash configuration
  LOGSTASH_APP_NAME: z.string().default('easy-flow--scrum-hub'),
  LOGSTASH_HOST: z.string().optional(),
  LOGSTASH_PORT: z.coerce.number().default(50000),
});

export type EnvironmentVariables = z.infer<typeof EnvVariablesSchema>;
