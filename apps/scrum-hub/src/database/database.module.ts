import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoggerOptions } from 'typeorm';
import { EnvironmentVariables } from '@/config';
import { ConfigService, isDevelopment } from '@libs/common/config';
import { Logger, PinoTypeOrmLogger } from '@libs/pino-logger';
import { DatabaseService } from './database.service';
import { Flow, FlowProcess, FlowProcessStep } from './flows';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [ConfigService, Logger],
      useFactory: (config: ConfigService<EnvironmentVariables>, logger: Logger) => ({
        type: 'postgres',
        host: config.getOrThrow('PG_HOST'),
        port: config.getOrThrow('PG_PORT'),
        password: config.getOrThrow('PG_PASSWORD'),
        username: config.getOrThrow('PG_USER'),
        database: config.getOrThrow('PG_DB'),
        // entities: [Flow, FlowProcess, FlowProcessStep],
        autoLoadEntities: true,
        synchronize: true, // TODO: use migrations in production
        logging: (isDevelopment() ? 'all' : 'info') as LoggerOptions,
        logger: new PinoTypeOrmLogger(logger),
      }),
    }),

    TypeOrmModule.forFeature([Flow, FlowProcess, FlowProcessStep]),
  ],

  providers: [DatabaseService],
  exports: [DatabaseService],
})
export class DatabaseModule {}
