import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Flow, FlowProcess, FlowProcessStep } from './flows';

@Injectable()
export class DatabaseService {
  constructor(
    @InjectRepository(Flow) public readonly flows: Repository<Flow>,
    @InjectRepository(FlowProcess) public readonly flowProcesses: Repository<FlowProcess>,
    @InjectRepository(FlowProcessStep) public readonly flowProcessSteps: Repository<FlowProcessStep>,
  ) {}
}
