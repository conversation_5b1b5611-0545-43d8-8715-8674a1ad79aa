import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { FlowProcessModel } from '@libs/models';
import type { FlowProcessStep } from './flow-process-step.entity';
import type { Flow } from './flow.entity';

@Entity('flow-processes')
export class FlowProcess implements UnknownProperties<FlowProcessModel> {
  @PrimaryGeneratedColumn()
  id: NumericEntityId;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'integer', nullable: true })
  parentId: NumericEntityId | null;

  @Column({ type: 'integer', nullable: true })
  prevId: NumericEntityId | null;

  @Column({ type: 'varchar' })
  mPath: string;

  @ManyToOne('Flow', 'processes', {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'flowId', referencedColumnName: 'id' })
  flow: Flow;

  @OneToMany('FlowProcessStep', 'process')
  steps: FlowProcessStep[];
}
