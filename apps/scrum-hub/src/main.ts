import { NestFactory } from '@nestjs/core';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { Logger, LoggerErrorInterceptor } from '@libs/pino-logger';
import { ScrumHubModule } from './app/scrum-hub.module';

async function bootstrap() {
  const port = Number(process.env.PORT ?? 3001);

  const app = await NestFactory.createMicroservice<MicroserviceOptions>(ScrumHubModule, {
    transport: Transport.TCP,
    options: {
      port,
    },
  });

  // Configure logger
  const logger = app.get(Logger);
  app.useLogger(logger);
  app.useGlobalInterceptors(new LoggerErrorInterceptor());

  await app.listen();

  logger.log(`
    🚀 Application is running on: http://localhost:${port}
    NODE_ENV: ${process.env.NODE_ENV}
  `);
}

void bootstrap();
