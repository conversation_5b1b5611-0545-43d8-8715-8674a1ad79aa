import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { FlowProcess } from '@/database';
import {
  type DeleteFlowProcessPayload,
  type PartialUpdateFlowProcessPayload,
  type UpdateFlowProcessPayload,
  type CreateFlowProcessPayload,
  type MoveFlowProcessPayload,
  type ReadFlowProcessByIdPayload,
  type ReadAllFlowProcessesPayload,
} from '@libs/models';
import {
  CreateFlowProcessUseCase,
  DeleteFlowProcessByIdUseCase,
  GetAllFlowProcessesUseCase,
  GetFlowProcessByIdUseCase,
  MoveFlowProcessUseCase,
  UpdateFlowProcessUseCase,
} from './use-cases';

@Controller('flows/:flowId/processes')
export class FlowProcessesController {
  constructor(
    private readonly createFlowProcessUseCase: CreateFlowProcessUseCase,
    private readonly updateFlowProcessUseCase: UpdateFlowProcessUseCase,
    private readonly deleteFlowProcessByIdUseCase: DeleteFlowProcessByIdUseCase,
    private readonly moveFlowProcessUseCase: MoveFlowProcessUseCase,
    private readonly getFlowProcessByIdUseCase: GetFlowProcessByIdUseCase,
    private readonly getAllFlowProcessesUseCase: GetAllFlowProcessesUseCase,
  ) {}

  @MessagePattern('flow-processes: create flow process')
  createProcess(@Payload() payload: CreateFlowProcessPayload): Promise<FlowProcess> {
    return this.createFlowProcessUseCase.execute(payload);
  }

  @MessagePattern('flow-processes: update flow process')
  updateProcess(
    @Payload() payload: UpdateFlowProcessPayload | PartialUpdateFlowProcessPayload,
  ): Promise<FlowProcess> {
    return this.updateFlowProcessUseCase.execute(payload);
  }

  @MessagePattern('flow-processes: delete flow process')
  deleteProcess(@Payload() payload: DeleteFlowProcessPayload): Promise<FlowProcess> {
    return this.deleteFlowProcessByIdUseCase.execute(payload);
  }

  @MessagePattern('flow-processes: move flow process')
  moveProcess(@Payload() payload: MoveFlowProcessPayload): Promise<FlowProcess> {
    return this.moveFlowProcessUseCase.execute(payload);
  }

  @MessagePattern('flow-processes: get flow process by id')
  getProcess(@Payload() payload: ReadFlowProcessByIdPayload): Promise<FlowProcess> {
    return this.getFlowProcessByIdUseCase.execute(payload);
  }

  @MessagePattern('flow-processes: get all flow processes')
  getAllProcesses(@Payload() payload: ReadAllFlowProcessesPayload): Promise<FlowProcess[]> {
    return this.getAllFlowProcessesUseCase.execute(payload);
  }
}
