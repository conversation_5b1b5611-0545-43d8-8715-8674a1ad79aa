import { Module } from '@nestjs/common';
import { DatabaseModule } from '@/database';
import { FlowProcessesController } from './flow-processes.controller';
import { FlowProcessesService } from './flow-processes.service';
import {
  CreateFlowProcessUseCase,
  DeleteFlowProcessByIdUseCase,
  GetAllFlowProcessesUseCase,
  GetFlowProcessByIdUseCase,
  MoveFlowProcessUseCase,
  UpdateFlowProcessUseCase,
} from './use-cases';

@Module({
  imports: [DatabaseModule],
  providers: [
    FlowProcessesService,

    CreateFlowProcessUseCase,
    DeleteFlowProcessByIdUseCase,
    GetAllFlowProcessesUseCase,
    GetFlowProcessByIdUseCase,
    MoveFlowProcessUseCase,
    UpdateFlowProcessUseCase,
  ],
  controllers: [FlowProcessesController],
})
export class FlowProcessesModule {}
