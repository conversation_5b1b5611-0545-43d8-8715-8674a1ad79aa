import { Injectable, Logger } from '@nestjs/common';
import { customAl<PERSON>bet } from 'nanoid';
import { EntityManager } from 'typeorm';
import { FlowProcess } from '@/database';
import { FlowProcessModel } from '@libs/models';

const alphabet = '0123456789abcdefghijklmnopqrstuvwxyz';
const nanoid = customAlphabet(alphabet, 6);

@Injectable()
export class FlowProcessesService {
  private readonly logger = new Logger(FlowProcessesService.name);

  constructor() {}

  /**
   * Removes undefined values from the values object and then assigns the values to the entity.
   * !!! This function mutates the entity !!!
   */
  public normalizeEntityValues(
    entity: FlowProcess,
    values: Partial<FlowProcess | FlowProcessModel>,
  ): FlowProcess {
    // exclude undefined values
    const normalizedValues = { ...values };
    for (const key in normalizedValues) {
      type Key = keyof typeof normalizedValues;
      if (normalizedValues[key as Key] === undefined) {
        delete normalizedValues[key as Key];
      }
    }

    // assign values
    Object.assign(entity, normalizedValues);
    if (entity.description === '') entity.description = null;

    return entity;
  }

  private generateMaterializedPath(parentMPath?: string): string {
    const path = nanoid();
    return parentMPath ? parentMPath + '/' + path : path;
  }

  /**
   * TODO: Potential issue with recursion.
   */
  public async generateUniqueMaterializedPath(
    entityManager: EntityManager,
    params: {
      flowId: NumericEntityId;
      parentMPath?: string;
    },
  ): Promise<string> {
    const { flowId, parentMPath } = params;
    this.logger.verbose({ msg: 'Generating mPath (recursively)', data: params });

    const mPath = this.generateMaterializedPath(parentMPath);

    this.logger.verbose({ msg: 'Checking if mPath already exists', data: { mPath } });

    const existingProcessWithSamePath = await entityManager.findOne(FlowProcess, {
      where: {
        flow: { id: flowId },
        mPath,
      },
    });

    if (existingProcessWithSamePath) {
      this.logger.warn({ msg: 'mPath already exists, generating new mPath', data: { mPath } });
      return await this.generateUniqueMaterializedPath(entityManager, { flowId: flowId, parentMPath });
    } else {
      this.logger.verbose({ msg: 'mPath is unique', data: { mPath } });
    }

    return mPath;
  }
}
