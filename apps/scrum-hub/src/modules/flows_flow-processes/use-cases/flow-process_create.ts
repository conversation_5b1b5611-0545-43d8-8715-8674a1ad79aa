import { ConflictException, Injectable, Logger } from '@nestjs/common';
import { IsNull, Not } from 'typeorm';
import { DatabaseService, FlowProcess } from '@/database';
import { NumericId } from '@libs/common/database';
import { type CreateFlowProcessPayload } from '@libs/models';
import { RedisLock } from '@libs/redis';
import { FlowProcessesService } from '../flow-processes.service';

@Injectable()
export class CreateFlowProcessUseCase implements UseCase {
  private readonly logger = new Logger(CreateFlowProcessUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly processesService: FlowProcessesService,
  ) {}

  private refinePayload({ flowId, dto }: CreateFlowProcessPayload) {
    const { name, description, parentId, prevId } = dto;
    return {
      flowId: NumericId(flowId),
      dto: { name, description, parentId: NumericId(parentId), prevId: NumericId(prevId) },
    };
  }

  @RedisLock(_ => 'project-x--flows')
  async execute(payload: CreateFlowProcessPayload): Promise<FlowProcess> {
    const { flowId, dto } = this.refinePayload(payload);
    const { name, description, parentId, prevId } = dto;

    this.logger.verbose({ msg: 'Started creating process', data: { flowId, dto } });

    const [process, existingRoot] = await this.db.flowProcesses.manager.transaction(async entityManager => {
      let parentMPath: string | undefined = undefined;

      // Find parent process and get its mPath
      if (parentId) {
        this.logger.verbose({ msg: 'Finding parent process', data: { parentId } });

        const parent = await entityManager.findOne(FlowProcess, {
          where: {
            id: parentId,
            flow: { id: flowId },
          },
        });
        if (!parent) {
          throw new ConflictException('Parent process not found', 'PARENT_PROCESS_NOT_FOUND');
        }

        this.logger.verbose({ msg: 'Parent process found', data: parent });

        parentMPath = parent.mPath;
      }

      this.logger.verbose({ msg: 'Generating mPath' });

      // Generate mPath
      const mPath = await this.processesService.generateUniqueMaterializedPath(entityManager, {
        flowId,
        parentMPath,
      });

      this.logger.verbose({ msg: 'Generated mPath', data: { mPath } });

      // Create process entity
      const process = entityManager.create(FlowProcess, {
        ...this.processesService.normalizeEntityValues(new FlowProcess(), {
          name,
          description,
          parentId,
          prevId,
          mPath,
        }),
        flow: { id: flowId },
      });

      this.logger.verbose({ msg: 'Process entity created', data: process });

      await entityManager.save(process);

      this.logger.verbose({ msg: 'Process saved', data: process });

      this.logger.verbose({ msg: 'Finding process with same prevId', data: { prevId } });

      // Find process with same prevId
      const existingRoot = await entityManager.findOne(FlowProcess, {
        where: {
          id: Not(process.id),
          flow: { id: flowId },
          parentId: parentId === null ? IsNull() : parentId,
          prevId: prevId === null ? IsNull() : prevId,
        },
      });

      // Shift processWithSamePrevId below if it exists
      if (existingRoot) {
        this.logger.verbose({ msg: 'Found process with same prevId', data: existingRoot });

        this.logger.verbose({
          msg: 'Updating prevId for existin process with same prevId',
          data: { processId: existingRoot.id, prevId: process.id },
        });

        existingRoot.prevId = process.id;
        await entityManager.save(existingRoot);
      } else {
        this.logger.verbose({ msg: 'No process with same prevId found' });
      }

      return [process, existingRoot];
    });

    this.logger.log({
      msg: 'Process created',
      data: {
        incomingData: { flowId, dto },
        createdProcess: process,
        modifiedData: { existingRoot },
      },
    });

    return process;
  }
}
