import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, FlowProcess } from '@/database';
import { NumericId } from '@libs/common/database';
import { ReadAllFlowProcessesPayload } from '@libs/models';

@Injectable()
export class GetAllFlowProcessesUseCase implements UseCase {
  private readonly logger = new Logger(GetAllFlowProcessesUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  private refinePayload({ flowId }: ReadAllFlowProcessesPayload) {
    return {
      flowId: NumericId(flowId),
    };
  }

  async execute(payload: ReadAllFlowProcessesPayload): Promise<FlowProcess[]> {
    const { flowId } = this.refinePayload(payload);

    this.logger.verbose({ msg: 'Started getting all processes', data: { flowId } });

    this.logger.verbose({ msg: 'Finding flow', data: { flowId } });

    const flow = await this.db.flows.findOne({
      where: { id: flowId },
    });

    if (!flow) {
      throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');
    }

    this.logger.verbose({ msg: 'Flow found', data: flow });

    this.logger.verbose({ msg: 'Finding all processes', data: { flowId } });

    const processes = await this.db.flowProcesses.find({
      where: {
        flow: { id: flowId },
      },
    });

    this.logger.verbose({
      msg: 'All processes found',
      data: {
        incomingData: { flowId },
        processesFound: processes,
      },
    });

    return processes;
  }
}
