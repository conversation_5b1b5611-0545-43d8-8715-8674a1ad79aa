import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, FlowProcess } from '@/database';
import { NumericId } from '@libs/common/database';
import { ReadFlowProcessByIdPayload } from '@libs/models';

@Injectable()
export class GetFlowProcessByIdUseCase implements UseCase {
  private readonly logger = new Logger(GetFlowProcessByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  private refinePayload({ flowId, processId }: ReadFlowProcessByIdPayload) {
    return {
      flowId: NumericId(flowId),
      processId: NumericId(processId),
    };
  }

  async execute(payload: ReadFlowProcessByIdPayload): Promise<FlowProcess> {
    const { flowId, processId } = this.refinePayload(payload);

    this.logger.verbose({ msg: 'Started getting process', data: { processId } });

    const process = await this.db.flowProcesses.findOne({
      where: {
        id: processId,
        flow: { id: flowId },
      },
    });

    if (!process) throw new NotFoundException('Process not found', 'PROCESS_NOT_FOUND');

    this.logger.verbose({
      msg: 'Process found',
      data: {
        incomingData: { processId },
        foundProcess: process,
      },
    });

    return process;
  }
}
