import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, FlowProcess } from '@/database';
import { NumericId } from '@libs/common/database';
import { type UpdateFlowProcessPayload, type PartialUpdateFlowProcessPayload } from '@libs/models';
import { RedisLock } from '@libs/redis';
import { FlowProcessesService } from '../flow-processes.service';

@Injectable()
export class UpdateFlowProcessUseCase implements UseCase {
  private readonly logger = new Logger(UpdateFlowProcessUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly processesService: FlowProcessesService,
  ) {}

  private refinePayload(payload: UpdateFlowProcessPayload | PartialUpdateFlowProcessPayload) {
    const { flowId, processId, dto } = payload;
    return {
      flowId: NumericId(flowId),
      processId: NumericId(processId),
      dto,
    };
  }

  @RedisLock(_ => 'project-x--flows')
  async execute(payload: UpdateFlowProcessPayload | PartialUpdateFlowProcessPayload): Promise<FlowProcess> {
    const { flowId, processId, dto } = this.refinePayload(payload);
    const { name, description } = dto;

    this.logger.verbose({ msg: 'Started updating process', data: { processId, dto } });

    this.logger.verbose({ msg: 'Finding process to update', data: { processId } });

    const process = await this.db.flowProcesses.findOne({
      where: {
        id: processId,
        flow: { id: flowId },
      },
    });

    if (!process) {
      throw new NotFoundException('Process not found', 'PROCESS_NOT_FOUND');
    }

    this.logger.verbose({ msg: 'process found', data: process });

    this.logger.verbose({ msg: 'Updating process', processId });

    const modifiedProcess = this.processesService.normalizeEntityValues(process, {
      name,
      description,
    });

    await this.db.flowProcesses.save(modifiedProcess);

    this.logger.log({
      msg: 'Process updated',
      data: {
        incomingData: { processId, dto },
        modifiedProcess,
      },
    });

    return modifiedProcess;
  }
}
