import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { Flow } from '@/database';
import {
  type CreateFlowPayload,
  type ReadFlowByIdPayload,
  type UpdateFlowPayload,
  type PartialUpdateFlowPayload,
  type DeleteFlowPayload,
  type MoveFlowPayload,
} from '@libs/models';
import {
  CreateFlowUseCase,
  DeleteFlowByIdUseCase,
  GetAllFlowsUseCase,
  GetFlowByIdUseCase,
  MoveFlowUseCase,
  UpdateFlowUseCase,
} from './use-cases';

@Controller('flows')
export class FlowsController {
  constructor(
    private readonly createFlowUseCase: CreateFlowUseCase,
    private readonly updateFlowUseCase: UpdateFlowUseCase,
    private readonly deleteFlowByIdUseCase: DeleteFlowByIdUseCase,
    private readonly moveFlowUseCase: MoveFlowUseCase,
    private readonly getFlowByIdUseCase: GetFlowByIdUseCase,
    private readonly getAllFlowsUseCase: GetAllFlowsUseCase,
  ) {}

  @MessagePattern('flows: create flow')
  createFlow(@Payload() payload: CreateFlowPayload): Promise<Flow> {
    return this.createFlowUseCase.execute(payload);
  }

  @MessagePattern('flows: update flow')
  updateFlow(@Payload() payload: UpdateFlowPayload | PartialUpdateFlowPayload): Promise<Flow> {
    return this.updateFlowUseCase.execute(payload);
  }

  @MessagePattern('flows: delete flow')
  deleteFlow(@Payload() payload: DeleteFlowPayload): Promise<Flow> {
    return this.deleteFlowByIdUseCase.execute(payload);
  }

  @MessagePattern('flows: move flow')
  moveFlow(@Payload() payload: MoveFlowPayload): Promise<Flow> {
    return this.moveFlowUseCase.execute(payload);
  }

  @MessagePattern('flows: get flow by id')
  getFlow(@Payload() payload: ReadFlowByIdPayload): Promise<Flow> {
    return this.getFlowByIdUseCase.execute(payload);
  }

  @MessagePattern('flows: get all flows')
  getAllFlows(): Promise<Flow[]> {
    return this.getAllFlowsUseCase.execute();
  }
}
