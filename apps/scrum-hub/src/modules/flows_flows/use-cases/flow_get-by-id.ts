import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, Flow } from '@/database';
import { NumericId } from '@libs/common/database';
import { type ReadFlowByIdPayload } from '@libs/models';

@Injectable()
export class GetFlowByIdUseCase implements UseCase {
  private readonly logger = new Logger(GetFlowByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  private refinePayload({ flowId }: ReadFlowByIdPayload) {
    return {
      flowId: NumericId(flowId),
    };
  }

  async execute(payload: ReadFlowByIdPayload): Promise<Flow> {
    const { flowId } = this.refinePayload(payload);

    this.logger.verbose({ msg: 'Started getting flow', data: { flowId } });

    const flow = await this.db.flows.findOne({
      where: { id: flowId },
    });

    if (!flow) {
      throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');
    }

    this.logger.verbose({
      msg: 'Flow found',
      data: {
        incomingData: { flowId },
        foundFlow: flow,
      },
    });

    return flow;
  }
}
