{
  "extends": "../../tsconfig.base.json",
  "files": [],
  "include": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.spec.json"
    }
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "esModuleInterop": true,
    "paths": {
      "@/*": ["./src/*"],

      // copy from tsconfig.base.json
      "@libs/common/api": ["../../libs/common/src/api/index.ts"],
      "@libs/common/config": ["../../libs/common/src/config/index.ts"],
      "@libs/common/database": ["../../libs/common/src/database/index.ts"],
      "@libs/common/swagger": ["../../libs/common/src/swagger/index.ts"],
      "@libs/models": ["../../libs/models/src/index.ts"],
      "@libs/pino-logger": ["../../libs/pino-logger/src/index.ts"],
      "@libs/redis": ["../../libs/redis/src/index.ts"],
      "@libs/swagger": ["../../libs/swagger/src/index.ts"]
    }
  }
}
