import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON>x<PERSON>,
  CallH<PERSON>ler,
  Logger,
  UnprocessableEntityException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, map } from 'rxjs';
import { ZodSchema } from 'zod';

// https://artosalminen.github.io/posts/how-to-manipulate-nestjs-response

export const EMPTY_RESPONSE = Symbol('empty-response');
export const SanitizeResponseWithZod = Reflector.createDecorator<ZodSchema | typeof EMPTY_RESPONSE>();

@Injectable()
export class ZodResponseInterceptor<T extends object> implements NestInterceptor<T> {
  private readonly logger = new Logger(ZodResponseInterceptor.name);

  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<T> {
    const schema = this.reflector.get(SanitizeResponseWithZod, context.getHandler());
    return next.handle().pipe(
      map((data: T) => {
        if (schema === undefined) return data;
        const isHttpResponse = 'data' in data;
        const payload = isHttpResponse ? data.data : data;
        try {
          const parsedData = schema === EMPTY_RESPONSE ? null : (schema.parse(payload) as unknown);
          return (isHttpResponse ? { ...data, data: parsedData } : parsedData) as T;
        } catch (error: unknown) {
          this.logger.error({
            msg: 'ZodResponseInterceptor: Error parsing response',
            error,
            data,
          });
          throw new UnprocessableEntityException('Response validation error', 'RESPONSE_VALIDATION_ERROR'); // 422 Unprocessable Entity
        }
      }),
    );
  }
}
