import { HttpStatus } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';

export const API_RPC_EXCEPTION_TYPE = 'ApiRpcException';

export class ApiRpcException extends RpcException {
  type: typeof API_RPC_EXCEPTION_TYPE;
  code: HttpStatus;
  override message: string;
  cause: string;

  constructor(options: { code: HttpStatus; message: string; cause: string }) {
    super({ type: API_RPC_EXCEPTION_TYPE, ...options });
  }
}

export const isApiRpcException = (error: unknown): error is ApiRpcException => {
  return (error as ApiRpcException).type === API_RPC_EXCEPTION_TYPE;
};
