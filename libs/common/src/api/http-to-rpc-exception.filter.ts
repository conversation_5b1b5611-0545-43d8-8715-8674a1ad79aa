import { ArgumentsHost, Catch, ExceptionFilter, HttpException } from '@nestjs/common';
import { throwError } from 'rxjs';
import { ApiRpcException } from './api-rpc-exception';

@Catch(HttpException)
export class HttpToRpcExceptionFilter implements ExceptionFilter {
  catch(httpException: HttpException, host: ArgumentsHost) {
    if (host.getType() === 'rpc') {
      const response = httpException.getResponse();

      const exception = new ApiRpcException({
        code: httpException.getStatus(),
        message: httpException.message,
        cause: typeof response === 'object' ? (response as { error: string }).error : response,
      });

      return throwError(() => exception.getError());
    } else {
      // TODO: test it
      const ctx = host.switchToHttp();
      const response = ctx.getResponse();
      return response.status(httpException.getStatus()).json(httpException.getResponse());
    }
  }
}
