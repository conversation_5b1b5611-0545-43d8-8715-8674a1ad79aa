import { Injectable, PipeTransform } from '@nestjs/common';
import { ZodValidationException } from 'nestjs-zod';
import { z } from 'zod';
import { isNumeric } from '../utils';

export const NumericId = <T extends number | string | null, R extends T extends null ? null : number>(
  id: T,
): R => {
  if (id === null) return null as R;

  if (typeof id === 'number') return id as unknown as R;

  if (!isNumeric(String(id))) throw new Error('Invalid numeric id');

  return Number(id) as R;
};

/**
 * Schema for numeric id as string
 */
export const numericIdSchema = () =>
  z.preprocess((value, ctx) => {
    const str = String(value);

    // return valid id
    if (isNumeric(str)) return str;

    // add issue to context
    ctx.addIssue({
      code: 'invalid_type',
      message: 'Id must be a number',
      expected: 'number',
      received: typeof value,
      path: [],
    });

    // return '' to skip z.string() validation and issue with required
    return '';
  }, z.string());

@Injectable()
export class NumericIdPipe implements PipeTransform {
  private readonly numIdSchema = numericIdSchema();

  transform(value: unknown) {
    const result = this.numIdSchema.safeParse(value);
    if (result.success) return result.data;
    else throw new ZodValidationException(result.error);
  }
}
