import { applyDecorators, HttpStatus } from '@nestjs/common';
import { ApiProperty, ApiResponse, ApiResponseExamples, ApiResponseOptions } from '@nestjs/swagger';

export class ApiErrorResponseDto {
  @ApiProperty()
  error: true;

  @ApiProperty({ enum: HttpStatus })
  statusCode: HttpStatus;

  @ApiProperty()
  message: string;

  @ApiProperty()
  cause: string;
}

export function ApiErrorResponse(
  statusCode: HttpStatus,
  message: string,
  cause?: string,
  options?: ApiResponseOptions,
) {
  return applyDecorators(
    ApiResponse({
      ...options,
      status: statusCode,
      example: {
        error: true,
        statusCode,
        message,
        cause,
      },
      type: ApiErrorResponseDto,
    }),
  );
}

export function ApiErrors(
  statusCode: HttpStatus,
  errors: { message: string; cause?: string }[],
  options?: ApiResponseOptions,
) {
  return applyDecorators(
    ApiResponse({
      ...options,
      status: statusCode,
      type: ApiErrorResponseDto,
      examples: errors.reduce(
        (list, { message, cause }) => {
          list[message] = {
            value: {
              error: true,
              statusCode,
              message,
              cause,
            },
            summary: message,
          };
          return list;
        },
        {} as Record<string, ApiResponseExamples>,
      ),
    }),
  );
}
