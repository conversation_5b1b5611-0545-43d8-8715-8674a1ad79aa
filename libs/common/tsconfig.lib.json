{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "declaration": true, "types": ["node"], "target": "es2021", "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*.ts", "../../env.d.ts"]}