import { createZodDto } from 'nestjs-zod';
import { FlowProcessSchema } from './flow-process';

export class FlowProcessDto extends createZodDto(FlowProcessSchema) {}

// create
const CreateFlowProcessSchema = FlowProcessSchema.pick({
  name: true,
  description: true,
  parentId: true,
  prevId: true,
});

export class CreateFlowProcessDto extends createZodDto(CreateFlowProcessSchema) {}

export interface CreateFlowProcessPayload {
  flowId: string;
  dto: CreateFlowProcessDto;
}

// read
export interface ReadFlowProcessByIdPayload {
  flowId: string;
  processId: string;
}

// read all
export interface ReadAllFlowProcessesPayload {
  flowId: string;
}

// update
const UpdateFlowProcessSchema = FlowProcessSchema.pick({
  name: true,
  description: true,
});

export class UpdateFlowProcessDto extends createZodDto(UpdateFlowProcessSchema) {}

export interface UpdateFlowProcessPayload {
  flowId: string;
  processId: string;
  dto: UpdateFlowProcessDto;
}

// partial update
const PartialUpdateFlowProcessSchema = UpdateFlowProcessSchema.partial();

export class PartialUpdateFlowProcessDto extends createZodDto(PartialUpdateFlowProcessSchema) {}

export interface PartialUpdateFlowProcessPayload {
  flowId: string;
  processId: string;
  dto: PartialUpdateFlowProcessDto;
}

// delete
export interface DeleteFlowProcessPayload {
  flowId: string;
  processId: string;
}

// move
const MoveFlowProcessSchema = FlowProcessSchema.pick({
  parentId: true,
  prevId: true,
});

export class MoveFlowProcessDto extends createZodDto(MoveFlowProcessSchema) {}

export interface MoveFlowProcessPayload {
  flowId: string;
  processId: string;
  dto: MoveFlowProcessDto;
}
