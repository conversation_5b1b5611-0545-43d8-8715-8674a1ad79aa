import { z } from 'zod';
import { numericIdSchema } from '@libs/common/database';

export const FlowProcessSchema = z.object({
  id: numericIdSchema().describe('Process id'),

  name: z.string().describe('Process name'),

  description: z.string().nullable().describe('Process description'),

  parentId: numericIdSchema().nullable().describe('Process parent id'),

  prevId: numericIdSchema().nullable().describe('Process previous id'),

  mPath: z.string().describe('Process materialized path'),
});

export type FlowProcessModel = z.infer<typeof FlowProcessSchema>;
