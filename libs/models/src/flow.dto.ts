import { createZodDto } from 'nestjs-zod';
import { FlowSchema } from './flow';

export class FlowDto extends createZodDto(FlowSchema) {}

// create
const CreateFlowSchema = FlowSchema.pick({
  name: true,
  description: true,
  prevId: true,
});

export class CreateFlowDto extends createZodDto(CreateFlowSchema) {}

export interface CreateFlowPayload {
  dto: CreateFlowDto;
}

// read
export interface ReadFlowByIdPayload {
  flowId: string;
}

// update
const UpdateFlowSchema = FlowSchema.pick({
  name: true,
  description: true,
});

export class UpdateFlowDto extends createZodDto(UpdateFlowSchema) {}

export interface UpdateFlowPayload {
  flowId: string;
  dto: UpdateFlowDto;
}

// partial update
const PartialUpdateFlowSchema = UpdateFlowSchema.partial();

export class PartialUpdateFlowDto extends createZodDto(PartialUpdateFlowSchema) {}

export interface PartialUpdateFlowPayload {
  flowId: string;
  dto: PartialUpdateFlowDto;
}

// delete
export interface DeleteFlowPayload {
  flowId: string;
}

// move
const MoveFlowSchema = FlowSchema.pick({
  prevId: true,
});

export class MoveFlowDto extends createZodDto(MoveFlowSchema) {}

export interface MoveFlowPayload {
  flowId: string;
  dto: MoveFlowDto;
}
