import { z } from 'zod';
import { numericIdSchema } from '@libs/common/database';

export const FlowSchema = z.object({
  id: numericIdSchema().describe('Flow id'),

  name: z.string().min(2, 'Name must be at least 2 characters').describe('Flow name'),

  description: z.string().nullable().describe('Flow description'),

  prevId: numericIdSchema().nullable().describe('Flow previous id'),
});

export type FlowModel = z.infer<typeof FlowSchema>;
