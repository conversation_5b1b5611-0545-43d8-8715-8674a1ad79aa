import { ConfigService } from '@nestjs/config';
import { Params } from 'nestjs-pino';
import { randomUUID } from 'node:crypto';
import { IncomingMessage } from 'node:http';
import { ecsFormat } from '@elastic/ecs-pino-format';
import { isDevelopment } from '@libs/common/config';

export const usePinoHttpOptions = (options: {
  configService: ConfigService;
  appName: string;
}): Params['pinoHttp'] => {
  const { configService, appName } = options;

  const baseOptions: Params['pinoHttp'] = {
    level: configService.get('LOG_LEVEL'),
    genReqId: (req: IncomingMessage) => req.headers['x-correlation-id'] || randomUUID(),
  };

  // Use pino-http-send if Logstash URL is provided
  if (configService.get<string>('LOGSTASH_HOST')) {
    return {
      ...baseOptions,

      transport: {
        target: 'pino-socket',
        options: {
          address: configService.get<string>('LOGSTASH_HOST'),
          port: configService.get<number>('LOGSTASH_PORT'),
          mode: 'tcp',
        },
      },

      ...ecsFormat({
        convertReqRes: true,
        apmIntegration: true,
        serviceName: appName,
      }),
      messageKey: 'msg',
      quietReqLogger: true,
      quietResLogger: true,

      // pino-http ("request completed" log message)
      customProps: (req: IncomingMessage) => ({
        url: req.url,
        method: req.method,
        httpVersion: req.httpVersion,
        remoteAddress: req.socket.remoteAddress,
        remotePort: req.socket.remotePort,
      }),
    };
  }

  // Use pino-pretty for development
  if (isDevelopment()) {
    return {
      ...baseOptions,
      transport: {
        target: 'pino-pretty',
        options: {
          translateTime: 'SYS:standard',
          colorize: true,
          singleLine: true,
          ignore:
            'pid,hostname,context,req.headers,req.params,req.query,req.remoteAddress,req.remotePort,res',
        },
      },
    };
  }

  return baseOptions;
};
