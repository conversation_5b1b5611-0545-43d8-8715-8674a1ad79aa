import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { Redis } from 'ioredis';
import { RedisLockService } from './redis-lock.service';
import { REDIS_SHUTDOWN_SERVICE, RedisModule } from './redis.module';
import { REDIS_CLIENT } from './redis.module';

describe('RedisModule', () => {
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '../test.env',
        }),

        RedisModule.forRootAsync({
          inject: [ConfigService],
          useFactory: async (config: ConfigService) => ({
            host: config.get<string>('REDIS_HOST'),
            port: config.get<number>('REDIS_PORT'),
          }),
        }),
      ],
    })
      .setLogger({
        log: () => {},
        error: () => {},
        warn: () => {},
        debug: () => {},
        verbose: () => {},
      })
      .compile();
  });

  it('should provide REDIS_CLIENT', () => {
    const redis = module.get<Redis>(REDIS_CLIENT);
    expect(redis).toBeDefined();
  });

  it('should provide RedisLockService', () => {
    const redisLockService = module.get<RedisLockService>(RedisLockService);
    expect(redisLockService).toBeDefined();
  });

  it('should lock and unlock a resource', async () => {
    const redisLockService = module.get<RedisLockService>(RedisLockService);
    const lock = await redisLockService.acquireLock('test');
    expect(lock).toBeDefined();
    await redisLockService.releaseLock(lock);
  });

  it('should throw error if it is already locked', async () => {
    const redisLockService = module.get<RedisLockService>(RedisLockService);
    const lock1 = await redisLockService.acquireLock('test');
    await expect(redisLockService.acquireLock('test')).rejects.toThrow();
    await redisLockService.releaseLock(lock1);
  });

  it('should throw error when on race condition', async () => {
    const redisLockService = module.get<RedisLockService>(RedisLockService);

    const lock1Promise = redisLockService.acquireLock('test');
    const lock2Promise = redisLockService.acquireLock('test');
    const lock3Promise = redisLockService.acquireLock('test');

    const results = await Promise.allSettled([lock1Promise, lock2Promise, lock3Promise]);

    const errors = results.filter(result => result.status === 'rejected');
    expect(errors.length).toBe(2);
  });

  it('should quit redis client on shutdown', async () => {
    const shutdownService = module.get<{ onApplicationShutdown: () => void }>(REDIS_SHUTDOWN_SERVICE);
    const quitSpy = jest.spyOn(shutdownService, 'onApplicationShutdown');
    await module.close();
    expect(quitSpy).toHaveBeenCalled();
  });
});
