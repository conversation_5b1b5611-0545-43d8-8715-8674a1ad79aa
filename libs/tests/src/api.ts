import { HttpStatus } from '@nestjs/common';
import { AxiosResponse } from 'axios';
import { expect } from '@jest/globals';
import type { ApiResponsePayload, ApiErrorPayload } from '@libs/common/api';

export const expectSuccessfulApiResponse = (
  res: AxiosResponse<ApiResponsePayload<unknown>>,
  status: HttpStatus = HttpStatus.OK,
) => {
  const data = res.data;
  expect(res.status).toBe(status);
  expect(data).toHaveProperty('success', true);
  expect(data).toHaveProperty('statusCode', res.status);
  expect(data).toHaveProperty('message');
};

export const expectFailedApiResponse = (
  res: AxiosResponse<ApiErrorPayload>,
  status: HttpStatus,
  cause?: string,
) => {
  const data = res.data;
  expect(res.status).toBe(status);
  expect(data).toHaveProperty('success', false);
  expect(data).toHaveProperty('statusCode', res.status);
  expect(data).toHaveProperty('message');
  if (cause) {
    expect(data).toHaveProperty('error', cause);
  }
};
