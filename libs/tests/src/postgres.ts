/* eslint-disable no-console */
import { Client, ClientConfig } from 'pg';

export class PgDbManager {
  db: string;
  client: Client;

  constructor(config: ClientConfig) {
    if (!config.database) throw new Error('Database name is required');
    this.db = config.database;
    this.client = new Client({ ...config, database: undefined });
  }

  async createDb() {
    await this.client.connect();

    const res = await this.client.query('SELECT 1 FROM pg_database WHERE datname = $1', [this.db]);
    if (res.rowCount === 0) {
      await this.client.query(`CREATE DATABASE ${this.db}`);
      console.log(`Created database ${this.db}`);
    } else {
      console.log(`Database ${this.db} already exists`);
    }

    await this.client.end();
  }

  async deleteDb() {
    await this.client.connect();

    // Close all connections to the database
    await this.client.query(
      `
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = $1 AND pid <> pg_backend_pid();
      `,
      [this.db],
    );

    await this.client.query(`DROP DATABASE ${this.db}`);
    console.log(`Deleted database ${this.db}`);

    await this.client.end();
  }
}
