{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "importHelpers": true, "target": "es2021", "module": "esnext", "lib": ["es2021", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@libs/common/api": ["libs/common/src/api/index.ts"], "@libs/common/config": ["libs/common/src/config/index.ts"], "@libs/common/database": ["libs/common/src/database/index.ts"], "@libs/common/swagger": ["libs/common/src/swagger/index.ts"], "@libs/models": ["libs/models/src/index.ts"], "@libs/pino-logger": ["libs/pino-logger/src/index.ts"], "@libs/swagger": ["libs/swagger/src/index.ts"], "@libs/tests/api": ["libs/tests/src/api.ts"], "@libs/tests/postgres": ["libs/tests/src/postgres.ts"]}}, "exclude": ["node_modules", "tmp"]}